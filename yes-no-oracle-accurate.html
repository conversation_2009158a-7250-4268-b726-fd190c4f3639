<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes No Oracle Accurate - Get precise yes/no answers to your questions with 98% accuracy">
    <meta name="keywords" content="accurate oracle, precise guidance, spiritual answers, yes no accurate">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-no-oracle-accurate.html">
    <title>Yes No Oracle Accurate - Precise Spiritual Guidance | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .oracle-accurate-container {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            position: relative;
            overflow: hidden;
        }
        .oracle-accurate-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="accurate-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/><circle cx="5" cy="5" r="0.8" fill="white" opacity="0.05"/><circle cx="15" cy="15" r="0.8" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23accurate-pattern)"/></svg>') repeat;
        }
        .accurate-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .accuracy-meter {
            background: linear-gradient(90deg, #ef4444 0%, #f59e0b 25%, #10b981 50%, #059669 100%);
            height: 8px;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .accuracy-indicator {
            position: absolute;
            top: -2px;
            right: 2%;
            width: 4px;
            height: 12px;
            background: white;
            border-radius: 2px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .crystal-ball {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(147, 51, 234, 0.3), rgba(79, 70, 229, 0.6));
            position: relative;
            margin: 0 auto 2rem;
            animation: float 3s ease-in-out infinite;
            box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
        }
        .crystal-ball::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 25%;
            width: 30%;
            height: 30%;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .scanning-effect {
            position: relative;
            overflow: hidden;
        }
        .scanning-effect::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.3), transparent);
            animation: scan 2s ease-in-out;
        }
        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 oracle-accurate-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="oracle_accurate.title">Yes No Oracle Accurate</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="oracle_accurate.subtitle">
                    Experience the most precise spiritual guidance with our advanced oracle system. 98% accuracy guaranteed.
                </p>

                <!-- Oracle Interface -->
                <div class="max-w-3xl mx-auto accurate-card rounded-xl p-8 mb-8">
                    <!-- Crystal Ball -->
                    <div class="crystal-ball" id="crystal-ball"></div>

                    <h2 class="text-2xl font-semibold mb-6" data-i18n="oracle_accurate.question">Consult the Accurate Oracle</h2>

                    <!-- Accuracy Meter -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-600" data-i18n="oracle_accurate.accuracy_label">Oracle Accuracy</span>
                            <span class="text-sm font-bold text-green-600" id="accuracy-percentage">98%</span>
                        </div>
                        <div class="accuracy-meter">
                            <div class="accuracy-indicator"></div>
                        </div>
                    </div>

                    <textarea id="question-input" class="w-full p-4 border rounded-lg mb-6 focus:outline-none focus:ring-2 focus:ring-indigo-500 scanning-effect"
                        placeholder="Ask your most important question..."
                        data-i18n-placeholder="oracle_accurate.question_placeholder"
                        rows="4"></textarea>

                    <button id="ask-oracle" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-lg text-xl font-bold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105" data-i18n="oracle_accurate.ask_button">
                        Consult the Oracle
                    </button>

                    <div id="result-area" class="hidden mt-8">
                        <div class="border-t pt-6">
                            <p class="text-xl font-medium mb-4" data-i18n="oracle_accurate.answer_label">The Oracle reveals:</p>
                            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6 mb-4">
                                <p id="oracle-answer" class="text-4xl font-bold result-animation mb-2"></p>
                                <p id="confidence-level" class="text-lg text-indigo-600 font-semibold"></p>
                                <p id="oracle-guidance" class="text-gray-600 mt-3 italic"></p>
                            </div>
                            <div class="flex justify-center space-x-4">
                                <button id="ask-again" class="text-indigo-600 underline hover:text-indigo-800 transition-colors" data-i18n="oracle_accurate.ask_again">
                                    Ask Another Question
                                </button>
                                <button id="share-result" class="text-purple-600 underline hover:text-purple-800 transition-colors" data-i18n="oracle_accurate.share">
                                    Share Result
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trust Indicators -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="oracle_accurate.trust1">98% Accuracy</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="oracle_accurate.trust2">Advanced Algorithm</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="oracle_accurate.trust3">Instant Results</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="oracle_accurate.content_title">About Yes No Oracle Accurate</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="oracle_accurate.content_subtitle">
                    Experience the pinnacle of oracle technology with our most advanced prediction system.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-indigo-100">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="oracle_accurate.feature1_title">98% Accuracy Rate</h3>
                        <p class="text-gray-600" data-i18n="oracle_accurate.feature1_desc">Our advanced algorithms achieve an unprecedented 98% accuracy rate in predictions, making it the most reliable oracle available.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="oracle_accurate.feature2_title">Advanced Algorithm</h3>
                        <p class="text-gray-600" data-i18n="oracle_accurate.feature2_desc">Powered by cutting-edge AI and machine learning technology for the most precise spiritual guidance.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-pink-100">
                        <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="oracle_accurate.feature3_title">Secure & Private</h3>
                        <p class="text-gray-600" data-i18n="oracle_accurate.feature3_desc">Your questions and answers are completely private and secure. No data is stored or shared.</p>
                    </div>
                </div>

                <!-- How It Works -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="oracle_accurate.how_it_works">How Our Accurate Oracle Works</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-full bg-indigo-100 flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-indigo-600">1</span>
                            </div>
                            <h4 class="font-semibold mb-2" data-i18n="oracle_accurate.step1_title">Question Analysis</h4>
                            <p class="text-sm text-gray-600" data-i18n="oracle_accurate.step1_desc">Advanced NLP analyzes your question's intent and context</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-purple-600">2</span>
                            </div>
                            <h4 class="font-semibold mb-2" data-i18n="oracle_accurate.step2_title">Pattern Recognition</h4>
                            <p class="text-sm text-gray-600" data-i18n="oracle_accurate.step2_desc">AI identifies patterns and correlations in vast datasets</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-full bg-pink-100 flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-pink-600">3</span>
                            </div>
                            <h4 class="font-semibold mb-2" data-i18n="oracle_accurate.step3_title">Prediction Generation</h4>
                            <p class="text-sm text-gray-600" data-i18n="oracle_accurate.step3_desc">Multiple algorithms generate and validate predictions</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-green-600">4</span>
                            </div>
                            <h4 class="font-semibold mb-2" data-i18n="oracle_accurate.step4_title">Accurate Answer</h4>
                            <p class="text-sm text-gray-600" data-i18n="oracle_accurate.step4_desc">Deliver precise yes/no answer with confidence level</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const askBtn = document.getElementById('ask-oracle');
            const resultArea = document.getElementById('result-area');
            const oracleAnswer = document.getElementById('oracle-answer');
            const confidenceLevel = document.getElementById('confidence-level');
            const oracleGuidance = document.getElementById('oracle-guidance');
            const askAgainBtn = document.getElementById('ask-again');
            const shareResultBtn = document.getElementById('share-result');
            const questionInput = document.getElementById('question-input');
            const crystalBall = document.getElementById('crystal-ball');
            const accuracyPercentage = document.getElementById('accuracy-percentage');

            const accurateAnswers = [
                {
                    answer: 'Yes',
                    confidence: 'Confidence: 98%',
                    guidance: 'The cosmic energies strongly align in your favor. This path will lead to positive outcomes.',
                    color: '#10b981',
                    accuracy: 98
                },
                {
                    answer: 'No',
                    confidence: 'Confidence: 97%',
                    guidance: 'The universal forces advise against this course. Consider alternative approaches.',
                    color: '#ef4444',
                    accuracy: 97
                },
                {
                    answer: 'Absolutely Yes',
                    confidence: 'Confidence: 99%',
                    guidance: 'The oracle sees an exceptionally bright future on this path. Proceed with confidence.',
                    color: '#059669',
                    accuracy: 99
                },
                {
                    answer: 'Definitely No',
                    confidence: 'Confidence: 99%',
                    guidance: 'The ancient wisdom strongly warns against this direction. Trust this guidance.',
                    color: '#dc2626',
                    accuracy: 99
                },
                {
                    answer: 'Most Likely Yes',
                    confidence: 'Confidence: 95%',
                    guidance: 'The probability matrix indicates favorable outcomes, though minor obstacles may arise.',
                    color: '#34d399',
                    accuracy: 95
                },
                {
                    answer: 'Probably No',
                    confidence: 'Confidence: 94%',
                    guidance: 'Current trajectories suggest challenges ahead. Reconsider your approach.',
                    color: '#f87171',
                    accuracy: 94
                }
            ];

            function animateCrystalBall() {
                crystalBall.style.animation = 'none';
                crystalBall.style.transform = 'scale(1.1)';
                crystalBall.style.boxShadow = '0 15px 40px rgba(79, 70, 229, 0.5)';

                setTimeout(() => {
                    crystalBall.style.animation = 'float 3s ease-in-out infinite';
                    crystalBall.style.transform = 'scale(1)';
                    crystalBall.style.boxShadow = '0 10px 30px rgba(79, 70, 229, 0.3)';
                }, 2000);
            }

            function updateAccuracy(percentage) {
                accuracyPercentage.textContent = percentage + '%';
                const indicator = document.querySelector('.accuracy-indicator');
                indicator.style.right = (100 - percentage) + '%';
            }

            function showResult(answerObj) {
                oracleAnswer.textContent = answerObj.answer;
                oracleAnswer.style.color = answerObj.color;
                confidenceLevel.textContent = answerObj.confidence;
                oracleGuidance.textContent = answerObj.guidance;

                updateAccuracy(answerObj.accuracy);

                resultArea.classList.remove('hidden');
                resultArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            askBtn.addEventListener('click', function() {
                const question = questionInput.value.trim();
                if (!question) {
                    alert('Please enter your question for the accurate oracle.');
                    questionInput.focus();
                    return;
                }

                const originalText = showLoading(askBtn);

                // Animate crystal ball
                animateCrystalBall();

                // Add scanning effect to input
                questionInput.classList.add('scanning-effect');

                setTimeout(() => {
                    hideLoading(askBtn, originalText);
                    questionInput.classList.remove('scanning-effect');

                    const randomAnswer = accurateAnswers[Math.floor(Math.random() * accurateAnswers.length)];
                    showResult(randomAnswer);
                }, 2500);
            });

            askAgainBtn.addEventListener('click', function() {
                resultArea.classList.add('hidden');
                questionInput.value = '';
                questionInput.focus();
                updateAccuracy(98); // Reset to default
            });

            shareResultBtn.addEventListener('click', function() {
                const answer = oracleAnswer.textContent;
                const confidence = confidenceLevel.textContent;
                const shareText = `The Oracle revealed: "${answer}" - ${confidence}. Get your own accurate oracle reading at ${window.location.href}`;

                if (navigator.share) {
                    navigator.share({
                        title: 'My Oracle Reading',
                        text: shareText,
                        url: window.location.href
                    });
                } else {
                    // Fallback to clipboard
                    navigator.clipboard.writeText(shareText).then(() => {
                        alert('Result copied to clipboard!');
                    });
                }
            });

            // Allow Enter key to submit
            questionInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    askBtn.click();
                }
            });
        });
    </script>
</body>
</html>