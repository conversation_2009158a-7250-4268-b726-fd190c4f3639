<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes No Oracle Accurate - Get precise yes/no answers to your questions">
    <title>Yes No Oracle Accurate | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Oracle section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="oracle_accurate.title">Yes No Oracle Accurate</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="mb-8">
                    <textarea class="w-full p-4 border rounded-lg focus:ring-2 focus:ring-indigo-500" 
                              placeholder="Ask your question..." 
                              data-i18n="[placeholder]oracle_accurate.question_placeholder"></textarea>
                </div>
                
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-lg text-xl transition-colors" 
                        id="askOracle" 
                        data-i18n="oracle_accurate.ask_button">
                    Ask the Oracle
                </button>
                
                <div class="result-area mt-8 hidden">
                    <p class="text-2xl font-bold oracle-answer" data-i18n="oracle_accurate.answer">Answer: </p>
                    <p class="text-lg mt-4 accuracy-text" data-i18n="oracle_accurate.accuracy">Accuracy: 98%</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="oracle_accurate.content_title">About Yes No Oracle Accurate</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="oracle_accurate.content1">Our accurate yes/no oracle provides highly precise answers to your questions.</p>
                
                <p data-i18n="oracle_accurate.content2">Using advanced algorithms, we achieve over 98% accuracy in our predictions.</p>
                
                <p data-i18n="oracle_accurate.content3">Perfect for decision making when you need clear yes or no answers.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const askBtn = document.getElementById('askOracle');
            const resultArea = document.querySelector('.result-area');
            const answerText = document.querySelector('.oracle-answer');
            
            askBtn.addEventListener('click', function() {
                // Show result
                resultArea.classList.remove('hidden');
                
                // Generate random answer (would be replaced with actual oracle logic)
                const answers = ['Yes', 'No'];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                answerText.textContent = `Answer: ${randomAnswer}`;
            });
        });
    </script>
</body>
</html>