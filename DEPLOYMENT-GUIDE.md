# 🚀 YesNoOracle.xyz 部署指南

## 📋 部署前准备

### 1. 项目文件检查
确保以下文件都存在且完整：

#### 核心HTML页面 (13个)
- ✅ `index.html` - 主页
- ✅ `button-maker.html` - 按钮生成器
- ✅ `yes-no-button.html` - 是否按钮
- ✅ `yes-no-oracle.html` - 是否预言机
- ✅ `yes-no-oracle-accurate.html` - 精准预言机
- ✅ `yesno.html` - 简洁版本
- ✅ `no-button.html` - 否定按钮
- ✅ `button-clicker.html` - 按钮点击器
- ✅ `game-buzzers.html` - 游戏抢答器
- ✅ `yes-and-no-button.html` - 是与否按钮
- ✅ `no-or-yes-button.html` - 否或是按钮
- ✅ `yes-or-no-tarot-accurate.html` - 精准塔罗
- ✅ `yes-or-no-tarot-wheel.html` - 塔罗轮盘
- ✅ `yes-or-mo.html` - 是或Mo

#### 共享资源文件
- ✅ `src/styles/shared-styles.css` - 共享样式
- ✅ `src/js/shared-functions.js` - 共享JavaScript
- ✅ `src/translations/en.json` - 多语言支持

### 2. 域名和SSL证书
- 域名：`yesnooracle.xyz`
- SSL证书：建议使用Let's Encrypt免费证书
- CDN：建议使用Cloudflare进行加速和安全防护

## 🌐 部署选项

### 选项1: 静态网站托管 (推荐)

#### A. Vercel (推荐) 🌟
```bash
# 1. 安装Vercel CLI
npm i -g vercel

# 2. 在项目根目录运行
vercel

# 3. 按照提示配置
# - Project name: yesnooracle
# - Framework: Other
# - Build command: (留空)
# - Output directory: ./
```

**优势：**
- 免费SSL证书
- 全球CDN
- 自动部署
- 优秀的性能

#### B. Netlify
```bash
# 1. 创建netlify.toml配置文件
# 2. 连接GitHub仓库
# 3. 自动部署
```

#### C. GitHub Pages
```bash
# 1. 推送代码到GitHub
# 2. 在Settings中启用Pages
# 3. 选择main分支
```

### 选项2: 传统Web服务器

#### A. Apache配置
```apache
<VirtualHost *:80>
    ServerName yesnooracle.xyz
    DocumentRoot /var/www/yesnooracle
    
    # 重定向到HTTPS
    Redirect permanent / https://yesnooracle.xyz/
</VirtualHost>

<VirtualHost *:443>
    ServerName yesnooracle.xyz
    DocumentRoot /var/www/yesnooracle
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # 缓存配置
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </LocationMatch>
</VirtualHost>
```

#### B. Nginx配置
```nginx
server {
    listen 80;
    server_name yesnooracle.xyz www.yesnooracle.xyz;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yesnooracle.xyz www.yesnooracle.xyz;
    
    root /var/www/yesnooracle;
    index index.html;
    
    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 缓存配置
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # HTML文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public";
    }
    
    # 压缩配置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

## 📁 文件上传清单

### 必需文件
```
/
├── index.html
├── button-maker.html
├── yes-no-button.html
├── yes-no-oracle.html
├── yes-no-oracle-accurate.html
├── yesno.html
├── no-button.html
├── button-clicker.html
├── game-buzzers.html
├── yes-and-no-button.html
├── no-or-yes-button.html
├── yes-or-no-tarot-accurate.html
├── yes-or-no-tarot-wheel.html
├── yes-or-mo.html
└── src/
    ├── styles/
    │   └── shared-styles.css
    ├── js/
    │   └── shared-functions.js
    └── translations/
        └── en.json
```

### 可选文件
```
├── robots.txt
├── sitemap.xml
├── favicon.ico
├── apple-touch-icon.png
└── manifest.json
```

## 🔧 部署后配置

### 1. DNS设置
```
A记录: yesnooracle.xyz -> 服务器IP
CNAME: www.yesnooracle.xyz -> yesnooracle.xyz
```

### 2. SSL证书 (Let's Encrypt)
```bash
# 安装certbot
sudo apt install certbot python3-certbot-apache

# 获取证书
sudo certbot --apache -d yesnooracle.xyz -d www.yesnooracle.xyz

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 性能优化
- 启用Gzip压缩
- 设置适当的缓存头
- 使用CDN加速
- 压缩图片资源

## 📊 监控和分析

### 1. Google Analytics
在每个HTML页面的`<head>`中添加：
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 2. Google Search Console
- 验证网站所有权
- 提交sitemap.xml
- 监控搜索性能

## 🚀 快速部署命令

### 使用Vercel (最简单)
```bash
# 1. 安装并登录
npm i -g vercel
vercel login

# 2. 部署
vercel --prod

# 3. 设置自定义域名
vercel domains add yesnooracle.xyz
```

### 使用传统服务器
```bash
# 1. 上传文件
rsync -avz --exclude='*.md' --exclude='*.xlsx' ./ user@server:/var/www/yesnooracle/

# 2. 设置权限
sudo chown -R www-data:www-data /var/www/yesnooracle
sudo chmod -R 755 /var/www/yesnooracle

# 3. 重启服务器
sudo systemctl restart apache2  # 或 nginx
```

## ✅ 部署验证

部署完成后，请检查：
- [ ] 所有13个页面都能正常访问
- [ ] CSS和JavaScript文件正常加载
- [ ] 响应式设计在移动设备上正常工作
- [ ] SSL证书正常工作 (https://)
- [ ] 页面加载速度良好
- [ ] 所有交互功能正常工作

## 🆘 常见问题

### 1. 文件路径问题
确保所有相对路径正确，特别是：
- `src/styles/shared-styles.css`
- `src/js/shared-functions.js`

### 2. HTTPS重定向
确保所有HTTP请求都重定向到HTTPS

### 3. 缓存问题
如果更新后页面没有变化，清除浏览器缓存或设置适当的缓存头

---

🎉 **恭喜！按照这个指南，你的YesNoOracle.xyz网站就可以成功部署上线了！**
