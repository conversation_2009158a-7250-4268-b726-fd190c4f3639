// Shared JavaScript functions for YesNoOracle project

// Multilingual support
const i18n = {
    en: {
        // Navigation
        "nav.home": "Home",
        "nav.tools": "Tools",
        "nav.how_it_works": "How It Works",
        "nav.features": "Features",
        "nav.testimonials": "Testimonials",
        "nav.pricing": "Pricing",
        "nav.faq": "FAQ",
        "nav.try_free": "Try Free",
        
        // Tools
        "tools.button_maker": "Button Maker",
        "tools.yes_no_button": "Yes No Button",
        "tools.yes_no_oracle": "Yes No Oracle",
        "tools.yes_no_oracle_accurate": "Yes No Oracle Accurate",
        "tools.yesno": "YesNo",
        "tools.no_button": "No Button",
        "tools.button_clicker": "Button Clicker",
        "tools.game_buzzers": "Game Buzzers",
        "tools.yes_and_no_button": "Yes And No Button",
        "tools.no_or_yes_button": "No Or Yes Button",
        "tools.yes_or_no_tarot_accurate": "Yes Or No Tarot Accurate",
        "tools.yes_or_no_tarot_wheel": "Yes Or No Tarot Wheel",
        "tools.yes_or_mo": "Yes Or Mo",
        
        // Common
        "common.back_to_home": "Back to Home",
        "common.related_tools": "Related Tools",
        "common.try_this_tool": "Try This Tool",
        "common.loading": "Loading...",
        "common.error": "Something went wrong. Please try again.",
        
        // Breadcrumb
        "breadcrumb.home": "Home",
        "breadcrumb.tools": "Tools"
    },
    zh: {
        // Navigation
        "nav.home": "首页",
        "nav.tools": "工具",
        "nav.how_it_works": "工作原理",
        "nav.features": "功能特点",
        "nav.testimonials": "用户评价",
        "nav.pricing": "价格方案",
        "nav.faq": "常见问题",
        "nav.try_free": "免费试用",
        
        // Tools
        "tools.button_maker": "按钮制作器",
        "tools.yes_no_button": "是否按钮",
        "tools.yes_no_oracle": "是否神谕",
        "tools.yes_no_oracle_accurate": "精准是否神谕",
        "tools.yesno": "是否",
        "tools.no_button": "否按钮",
        "tools.button_clicker": "按钮点击器",
        "tools.game_buzzers": "游戏蜂鸣器",
        "tools.yes_and_no_button": "是和否按钮",
        "tools.no_or_yes_button": "否或是按钮",
        "tools.yes_or_no_tarot_accurate": "精准是否塔罗",
        "tools.yes_or_no_tarot_wheel": "是否塔罗轮盘",
        "tools.yes_or_mo": "是或否",
        
        // Common
        "common.back_to_home": "返回首页",
        "common.related_tools": "相关工具",
        "common.try_this_tool": "试用此工具",
        "common.loading": "加载中...",
        "common.error": "出现错误，请重试。",
        
        // Breadcrumb
        "breadcrumb.home": "首页",
        "breadcrumb.tools": "工具"
    }
};

// Current language
let currentLanguage = 'en';

// Initialize shared functionality
function initSharedFunctions() {
    initLanguageSelector();
    initBreadcrumb();
    initRelatedTools();
    initMobileMenu();
}

// Language selector functionality
function initLanguageSelector() {
    const languageSelector = document.getElementById('language-selector');
    if (languageSelector) {
        // Load saved language preference
        const savedLanguage = localStorage.getItem('preferred-language') || 'en';
        languageSelector.value = savedLanguage;
        currentLanguage = savedLanguage;
        updateLanguage(savedLanguage);
        
        languageSelector.addEventListener('change', function() {
            const selectedLanguage = this.value;
            currentLanguage = selectedLanguage;
            localStorage.setItem('preferred-language', selectedLanguage);
            updateLanguage(selectedLanguage);
        });
    }
}

// Update page language
function updateLanguage(language) {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (i18n[language] && i18n[language][key]) {
            element.textContent = i18n[language][key];
        }
    });
    
    // Update placeholders
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        if (i18n[language] && i18n[language][key]) {
            element.placeholder = i18n[language][key];
        }
    });
}

// Initialize breadcrumb navigation
function initBreadcrumb() {
    const breadcrumbContainer = document.getElementById('breadcrumb-container');
    if (breadcrumbContainer) {
        const currentPage = getCurrentPageInfo();
        const breadcrumbHTML = generateBreadcrumb(currentPage);
        breadcrumbContainer.innerHTML = breadcrumbHTML;
    }
}

// Get current page information
function getCurrentPageInfo() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();
    
    const pageMap = {
        'index.html': { name: 'Home', category: null },
        'button-maker.html': { name: 'Button Maker', category: 'Tools' },
        'yes-no-button.html': { name: 'Yes No Button', category: 'Tools' },
        'yes-no-oracle.html': { name: 'Yes No Oracle', category: 'Tools' },
        'yes-no-oracle-accurate.html': { name: 'Yes No Oracle Accurate', category: 'Tools' },
        'yesno.html': { name: 'YesNo', category: 'Tools' },
        'no-button.html': { name: 'No Button', category: 'Tools' },
        'button-clicker.html': { name: 'Button Clicker', category: 'Tools' },
        'game-buzzers.html': { name: 'Game Buzzers', category: 'Tools' },
        'yes-and-no-button.html': { name: 'Yes And No Button', category: 'Tools' },
        'no-or-yes-button.html': { name: 'No Or Yes Button', category: 'Tools' },
        'yes-or-no-tarot-accurate.html': { name: 'Yes Or No Tarot Accurate', category: 'Tools' },
        'yes-or-no-tarot-wheel.html': { name: 'Yes Or No Tarot Wheel', category: 'Tools' },
        'yes-or-mo.html': { name: 'Yes Or Mo', category: 'Tools' }
    };
    
    return pageMap[filename] || { name: 'Unknown', category: null };
}

// Generate breadcrumb HTML
function generateBreadcrumb(currentPage) {
    let breadcrumb = '<nav class="breadcrumb"><div class="container mx-auto px-4"><div class="flex items-center text-sm">';
    
    // Home link
    breadcrumb += '<a href="index.html" class="breadcrumb-item" data-i18n="breadcrumb.home">Home</a>';
    
    // Category link (if exists)
    if (currentPage.category) {
        breadcrumb += '<span class="breadcrumb-separator">›</span>';
        breadcrumb += `<span class="breadcrumb-item" data-i18n="breadcrumb.${currentPage.category.toLowerCase()}">${currentPage.category}</span>`;
    }
    
    // Current page
    if (currentPage.name !== 'Home') {
        breadcrumb += '<span class="breadcrumb-separator">›</span>';
        breadcrumb += `<span class="text-gray-900 font-medium">${currentPage.name}</span>`;
    }
    
    breadcrumb += '</div></div></nav>';
    return breadcrumb;
}

// Initialize related tools section
function initRelatedTools() {
    const relatedToolsContainer = document.getElementById('related-tools-container');
    if (relatedToolsContainer) {
        const currentPage = getCurrentPageInfo();
        const relatedTools = getRelatedTools(currentPage);
        const relatedToolsHTML = generateRelatedToolsHTML(relatedTools);
        relatedToolsContainer.innerHTML = relatedToolsHTML;
    }
}

// Get related tools based on current page
function getRelatedTools(currentPage) {
    const allTools = [
        { name: 'Button Maker', url: 'button-maker.html', description: 'Create custom buttons' },
        { name: 'Yes No Button', url: 'yes-no-button.html', description: 'Simple yes/no decision' },
        { name: 'Yes No Oracle', url: 'yes-no-oracle.html', description: 'Divine guidance tool' },
        { name: 'Yes No Oracle Accurate', url: 'yes-no-oracle-accurate.html', description: 'Precise oracle answers' },
        { name: 'Button Clicker', url: 'button-clicker.html', description: 'Interactive button game' },
        { name: 'Game Buzzers', url: 'game-buzzers.html', description: 'Game show buzzers' }
    ];
    
    // Filter out current page and return 3 random tools
    const filtered = allTools.filter(tool => !window.location.pathname.includes(tool.url));
    return filtered.sort(() => 0.5 - Math.random()).slice(0, 3);
}

// Generate related tools HTML
function generateRelatedToolsHTML(tools) {
    let html = '<div class="grid grid-cols-1 md:grid-cols-3 gap-6">';
    
    tools.forEach(tool => {
        html += `
            <div class="tool-card">
                <h3 class="text-lg font-semibold mb-2">${tool.name}</h3>
                <p class="text-gray-600 mb-4">${tool.description}</p>
                <a href="${tool.url}" class="btn-primary" data-i18n="common.try_this_tool">Try This Tool</a>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// Initialize mobile menu
function initMobileMenu() {
    // Add mobile menu toggle functionality if needed
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

// Utility function to show loading state
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        const originalText = element.textContent;
        element.innerHTML = '<span class="spinner"></span>' + (i18n[currentLanguage]['common.loading'] || 'Loading...');
        return originalText;
    }
}

// Utility function to hide loading state
function hideLoading(element, originalText) {
    if (element) {
        element.classList.remove('loading');
        element.textContent = originalText;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initSharedFunctions);
