// Shared JavaScript functions for YesNoOracle project

// Multilingual support
const i18n = {
    en: {
        // Navigation
        "nav.home": "Home",
        "nav.tools": "Tools",
        "nav.how_it_works": "How It Works",
        "nav.features": "Features",
        "nav.testimonials": "Testimonials",
        "nav.pricing": "Pricing",
        "nav.faq": "FAQ",
        "nav.try_free": "Try Free",
        
        // Tools
        "tools.button_maker": "Button Maker",
        "tools.yes_no_button": "Yes No Button",
        "tools.yes_no_oracle": "Yes No Oracle",
        "tools.yes_no_oracle_accurate": "Yes No Oracle Accurate",
        "tools.yesno": "YesNo",
        "tools.no_button": "No Button",
        "tools.button_clicker": "Button Clicker",
        "tools.game_buzzers": "Game Buzzers",
        "tools.yes_and_no_button": "Yes And No Button",
        "tools.no_or_yes_button": "No Or Yes Button",
        "tools.yes_or_no_tarot_accurate": "Yes Or No Tarot Accurate",
        "tools.yes_or_no_tarot_wheel": "Yes Or No Tarot Wheel",
        "tools.yes_or_mo": "Yes Or Mo",

        // Button Maker
        "button_maker.title": "Create Custom Buttons in Seconds",
        "button_maker.subtitle": "Design professional buttons without any coding knowledge. Perfect for websites, apps, and digital projects.",
        "button_maker.customize": "Customize Your Button",
        "button_maker.button_text": "Button Text",
        "button_maker.text_placeholder": "Click me",
        "button_maker.button_color": "Button Color",
        "button_maker.button_size": "Button Size",
        "button_maker.size_small": "Small",
        "button_maker.size_medium": "Medium",
        "button_maker.size_large": "Large",
        "button_maker.button_style": "Button Style",
        "button_maker.style_solid": "Solid",
        "button_maker.style_outline": "Outline",
        "button_maker.style_ghost": "Ghost",
        "button_maker.border_radius": "Border Radius",
        "button_maker.preview": "Button Preview",
        "button_maker.css_code": "CSS Code",
        "button_maker.copy_css": "Copy CSS Code",
        "button_maker.download": "Download as HTML",
        "button_maker.features_title": "Why Use Our Button Maker?",
        "button_maker.features_subtitle": "Create professional buttons with our easy-to-use online tool. No design experience required.",
        "button_maker.feature1_title": "No Coding Required",
        "button_maker.feature1_desc": "Create professional buttons without writing a single line of code. Perfect for designers and developers alike.",
        "button_maker.feature2_title": "Instant Results",
        "button_maker.feature2_desc": "See your button changes in real-time as you customize it. What you see is what you get.",
        "button_maker.feature3_title": "Free Forever",
        "button_maker.feature3_desc": "Our button maker is completely free with no hidden costs. Create unlimited buttons for all your projects.",
        "button_maker.feature4_title": "Export Ready Code",
        "button_maker.feature4_desc": "Get clean, optimized CSS code that you can copy and paste directly into your projects.",
        "button_maker.feature5_title": "Multiple Styles",
        "button_maker.feature5_desc": "Choose from solid, outline, and ghost button styles to match your design needs.",
        "button_maker.feature6_title": "Mobile Responsive",
        "button_maker.feature6_desc": "All generated buttons are mobile-responsive and work perfectly on any device size.",
        
        // Common
        "common.back_to_home": "Back to Home",
        "common.related_tools": "Related Tools",
        "common.try_this_tool": "Try This Tool",
        "common.loading": "Loading...",
        "common.error": "Something went wrong. Please try again.",
        
        // Breadcrumb
        "breadcrumb.home": "Home",
        "breadcrumb.tools": "Tools"
    },
    zh: {
        // Navigation
        "nav.home": "首页",
        "nav.tools": "工具",
        "nav.how_it_works": "工作原理",
        "nav.features": "功能特点",
        "nav.testimonials": "用户评价",
        "nav.pricing": "价格方案",
        "nav.faq": "常见问题",
        "nav.try_free": "免费试用",
        
        // Tools
        "tools.button_maker": "按钮制作器",
        "tools.yes_no_button": "是否按钮",
        "tools.yes_no_oracle": "是否神谕",
        "tools.yes_no_oracle_accurate": "精准是否神谕",
        "tools.yesno": "是否",
        "tools.no_button": "否按钮",
        "tools.button_clicker": "按钮点击器",
        "tools.game_buzzers": "游戏蜂鸣器",
        "tools.yes_and_no_button": "是和否按钮",
        "tools.no_or_yes_button": "否或是按钮",
        "tools.yes_or_no_tarot_accurate": "精准是否塔罗",
        "tools.yes_or_no_tarot_wheel": "是否塔罗轮盘",
        "tools.yes_or_mo": "是或否",

        // Button Maker
        "button_maker.title": "几秒钟创建自定义按钮",
        "button_maker.subtitle": "无需编程知识即可设计专业按钮。适用于网站、应用和数字项目。",
        "button_maker.customize": "自定义您的按钮",
        "button_maker.button_text": "按钮文本",
        "button_maker.text_placeholder": "点击我",
        "button_maker.button_color": "按钮颜色",
        "button_maker.button_size": "按钮大小",
        "button_maker.size_small": "小",
        "button_maker.size_medium": "中",
        "button_maker.size_large": "大",
        "button_maker.button_style": "按钮样式",
        "button_maker.style_solid": "实心",
        "button_maker.style_outline": "轮廓",
        "button_maker.style_ghost": "幽灵",
        "button_maker.border_radius": "圆角半径",
        "button_maker.preview": "按钮预览",
        "button_maker.css_code": "CSS代码",
        "button_maker.copy_css": "复制CSS代码",
        "button_maker.download": "下载为HTML",
        "button_maker.features_title": "为什么使用我们的按钮制作器？",
        "button_maker.features_subtitle": "使用我们易于使用的在线工具创建专业按钮。无需设计经验。",
        "button_maker.feature1_title": "无需编程",
        "button_maker.feature1_desc": "无需编写任何代码即可创建专业按钮。适合设计师和开发者。",
        "button_maker.feature2_title": "即时结果",
        "button_maker.feature2_desc": "实时查看按钮更改效果。所见即所得。",
        "button_maker.feature3_title": "永久免费",
        "button_maker.feature3_desc": "我们的按钮制作器完全免费，无隐藏费用。为所有项目创建无限按钮。",
        "button_maker.feature4_title": "导出就绪代码",
        "button_maker.feature4_desc": "获得干净、优化的CSS代码，可直接复制粘贴到您的项目中。",
        "button_maker.feature5_title": "多种样式",
        "button_maker.feature5_desc": "选择实心、轮廓和幽灵按钮样式以匹配您的设计需求。",
        "button_maker.feature6_title": "移动响应式",
        "button_maker.feature6_desc": "所有生成的按钮都是移动响应式的，在任何设备尺寸上都能完美工作。",
        
        // Common
        "common.back_to_home": "返回首页",
        "common.related_tools": "相关工具",
        "common.try_this_tool": "试用此工具",
        "common.loading": "加载中...",
        "common.error": "出现错误，请重试。",
        
        // Breadcrumb
        "breadcrumb.home": "首页",
        "breadcrumb.tools": "工具"
    }
};

// Current language
let currentLanguage = 'en';

// Initialize shared functionality
function initSharedFunctions() {
    initLanguageSelector();
    initBreadcrumb();
    initRelatedTools();
    initMobileMenu();
}

// Language selector functionality
function initLanguageSelector() {
    const languageSelector = document.getElementById('language-selector');
    if (languageSelector) {
        // Load saved language preference
        const savedLanguage = localStorage.getItem('preferred-language') || 'en';
        languageSelector.value = savedLanguage;
        currentLanguage = savedLanguage;
        updateLanguage(savedLanguage);
        
        languageSelector.addEventListener('change', function() {
            const selectedLanguage = this.value;
            currentLanguage = selectedLanguage;
            localStorage.setItem('preferred-language', selectedLanguage);
            updateLanguage(selectedLanguage);
        });
    }
}

// Update page language
function updateLanguage(language) {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (i18n[language] && i18n[language][key]) {
            element.textContent = i18n[language][key];
        }
    });
    
    // Update placeholders
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        if (i18n[language] && i18n[language][key]) {
            element.placeholder = i18n[language][key];
        }
    });
}

// Initialize breadcrumb navigation
function initBreadcrumb() {
    const breadcrumbContainer = document.getElementById('breadcrumb-container');
    if (breadcrumbContainer) {
        const currentPage = getCurrentPageInfo();
        const breadcrumbHTML = generateBreadcrumb(currentPage);
        breadcrumbContainer.innerHTML = breadcrumbHTML;
    }
}

// Get current page information
function getCurrentPageInfo() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();
    
    const pageMap = {
        'index.html': { name: 'Home', category: null },
        'button-maker.html': { name: 'Button Maker', category: 'Tools' },
        'yes-no-button.html': { name: 'Yes No Button', category: 'Tools' },
        'yes-no-oracle.html': { name: 'Yes No Oracle', category: 'Tools' },
        'yes-no-oracle-accurate.html': { name: 'Yes No Oracle Accurate', category: 'Tools' },
        'yesno.html': { name: 'YesNo', category: 'Tools' },
        'no-button.html': { name: 'No Button', category: 'Tools' },
        'button-clicker.html': { name: 'Button Clicker', category: 'Tools' },
        'game-buzzers.html': { name: 'Game Buzzers', category: 'Tools' },
        'yes-and-no-button.html': { name: 'Yes And No Button', category: 'Tools' },
        'no-or-yes-button.html': { name: 'No Or Yes Button', category: 'Tools' },
        'yes-or-no-tarot-accurate.html': { name: 'Yes Or No Tarot Accurate', category: 'Tools' },
        'yes-or-no-tarot-wheel.html': { name: 'Yes Or No Tarot Wheel', category: 'Tools' },
        'yes-or-mo.html': { name: 'Yes Or Mo', category: 'Tools' }
    };
    
    return pageMap[filename] || { name: 'Unknown', category: null };
}

// Generate breadcrumb HTML
function generateBreadcrumb(currentPage) {
    let breadcrumb = '<nav class="breadcrumb"><div class="container mx-auto px-4"><div class="flex items-center text-sm">';
    
    // Home link
    breadcrumb += '<a href="index.html" class="breadcrumb-item" data-i18n="breadcrumb.home">Home</a>';
    
    // Category link (if exists)
    if (currentPage.category) {
        breadcrumb += '<span class="breadcrumb-separator">›</span>';
        breadcrumb += `<span class="breadcrumb-item" data-i18n="breadcrumb.${currentPage.category.toLowerCase()}">${currentPage.category}</span>`;
    }
    
    // Current page
    if (currentPage.name !== 'Home') {
        breadcrumb += '<span class="breadcrumb-separator">›</span>';
        breadcrumb += `<span class="text-gray-900 font-medium">${currentPage.name}</span>`;
    }
    
    breadcrumb += '</div></div></nav>';
    return breadcrumb;
}

// Initialize related tools section
function initRelatedTools() {
    const relatedToolsContainer = document.getElementById('related-tools-container');
    if (relatedToolsContainer) {
        const currentPage = getCurrentPageInfo();
        const relatedTools = getRelatedTools(currentPage);
        const relatedToolsHTML = generateRelatedToolsHTML(relatedTools);
        relatedToolsContainer.innerHTML = relatedToolsHTML;
    }
}

// Get related tools based on current page
function getRelatedTools(currentPage) {
    const allTools = [
        { name: 'Button Maker', url: 'button-maker.html', description: 'Create custom buttons' },
        { name: 'Yes No Button', url: 'yes-no-button.html', description: 'Simple yes/no decision' },
        { name: 'Yes No Oracle', url: 'yes-no-oracle.html', description: 'Divine guidance tool' },
        { name: 'Yes No Oracle Accurate', url: 'yes-no-oracle-accurate.html', description: 'Precise oracle answers' },
        { name: 'Button Clicker', url: 'button-clicker.html', description: 'Interactive button game' },
        { name: 'Game Buzzers', url: 'game-buzzers.html', description: 'Game show buzzers' }
    ];
    
    // Filter out current page and return 3 random tools
    const filtered = allTools.filter(tool => !window.location.pathname.includes(tool.url));
    return filtered.sort(() => 0.5 - Math.random()).slice(0, 3);
}

// Generate related tools HTML
function generateRelatedToolsHTML(tools) {
    let html = '<div class="grid grid-cols-1 md:grid-cols-3 gap-6">';
    
    tools.forEach(tool => {
        html += `
            <div class="tool-card">
                <h3 class="text-lg font-semibold mb-2">${tool.name}</h3>
                <p class="text-gray-600 mb-4">${tool.description}</p>
                <a href="${tool.url}" class="btn-primary" data-i18n="common.try_this_tool">Try This Tool</a>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// Initialize mobile menu
function initMobileMenu() {
    // Add mobile menu toggle functionality if needed
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

// Utility function to show loading state
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        const originalText = element.textContent;
        element.innerHTML = '<span class="spinner"></span>' + (i18n[currentLanguage]['common.loading'] || 'Loading...');
        return originalText;
    }
}

// Utility function to hide loading state
function hideLoading(element, originalText) {
    if (element) {
        element.classList.remove('loading');
        element.textContent = originalText;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initSharedFunctions);
