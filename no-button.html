<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="No Button - A simple tool to say no when you need to">
    <title>No Button | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- No Button section -->
    <section class="py-20 bg-gradient-to-br from-red-50 to-pink-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="nobutton.title">No Button</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <button class="bg-red-600 text-white px-12 py-8 rounded-full text-3xl font-bold hover:bg-red-700 transition-colors" data-i18n="nobutton.press">PRESS TO SAY NO</button>
                
                <div class="result-area hidden mt-8">
                    <p class="text-5xl font-bold result-text" style="color: #dc2626;">NO!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="nobutton.content_title">About The No Button</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="nobutton.content1">Sometimes the hardest word to say is "no". Our No Button makes it easy to practice setting boundaries and saying no when you need to.</p>
                
                <p data-i18n="nobutton.content2">Whether you're dealing with difficult requests, peer pressure, or just need to practice asserting yourself, this simple tool helps build confidence in saying no.</p>
                
                <p data-i18n="nobutton.content3">The No Button is designed to be a fun yet powerful reminder that saying no is sometimes the healthiest choice you can make.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const noBtn = document.querySelector('.no-btn');
            const resultArea = document.querySelector('.result-area');
            
            noBtn.addEventListener('click', function() {
                resultArea.classList.remove('hidden');
                setTimeout(() => {
                    resultArea.classList.add('hidden');
                }, 2000);
            });
        });
    </script>
</body>
</html>