<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="No Button - Practice saying no and setting boundaries with confidence">
    <meta name="keywords" content="no button, say no, boundaries, assertiveness, confidence">
    <link rel="canonical" href="https://yesnooracle.xyz/no-button.html">
    <title>No Button - Practice Saying No | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .no-container {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
            position: relative;
            overflow: hidden;
        }
        .no-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="no-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23no-pattern)"/></svg>') repeat;
        }
        .no-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .no-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            box-shadow: 0 10px 30px rgba(220, 38, 38, 0.3);
        }
        .no-button:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(220, 38, 38, 0.4);
        }
        .no-button:active {
            transform: scale(0.95);
        }
        .no-button.pressed {
            animation: noPress 0.8s ease-out;
        }
        @keyframes noPress {
            0% { transform: scale(1); }
            25% { transform: scale(0.9); }
            50% { transform: scale(1.15); }
            75% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .no-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }
        .no-button.pressed::before {
            width: 400px;
            height: 400px;
        }
        .result-display {
            animation: resultPop 0.6s ease-out;
        }
        @keyframes resultPop {
            0% { transform: scale(0) rotate(-180deg); opacity: 0; }
            50% { transform: scale(1.2) rotate(-90deg); opacity: 1; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        .no-text {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
            animation: noGlow 2s ease-in-out infinite alternate;
        }
        @keyframes noGlow {
            from { filter: drop-shadow(0 0 10px #dc2626); }
            to { filter: drop-shadow(0 0 20px #b91c1c); }
        }
        .confidence-meter {
            background: linear-gradient(90deg, #fef3c7 0%, #fbbf24 50%, #f59e0b 100%);
            border-radius: 10px;
            height: 8px;
            position: relative;
            overflow: hidden;
        }
        .confidence-indicator {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #dc2626, #b91c1c);
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
        }
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 no-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="nobutton.title">The No Button</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="nobutton.subtitle">
                    Practice saying no with confidence. Build boundaries and assertiveness skills.
                </p>

                <!-- No Button Interface -->
                <div class="max-w-3xl mx-auto no-card rounded-xl p-8 mb-8">
                    <!-- Confidence Meter -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4" data-i18n="nobutton.confidence_title">Confidence Level</h3>
                        <div class="confidence-meter">
                            <div id="confidence-indicator" class="confidence-indicator"></div>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600 mt-2">
                            <span data-i18n="nobutton.confidence_low">Hesitant</span>
                            <span id="confidence-percentage" class="font-semibold">0%</span>
                            <span data-i18n="nobutton.confidence_high">Confident</span>
                        </div>
                    </div>

                    <!-- Main No Button -->
                    <div class="mb-8">
                        <button id="no-button" class="no-button bg-gradient-to-br from-red-500 to-red-700 text-white px-16 py-12 rounded-full text-4xl font-bold hover:from-red-600 hover:to-red-800 transition-all duration-300 shadow-2xl" data-i18n="nobutton.press">
                            SAY NO!
                        </button>
                    </div>

                    <!-- Result Display -->
                    <div id="result-area" class="hidden mb-8">
                        <div class="result-display bg-gradient-to-r from-red-100 to-pink-100 rounded-lg p-6">
                            <div id="no-text" class="no-text text-6xl font-bold mb-4">NO!</div>
                            <div id="encouragement" class="text-lg text-gray-700 font-medium"></div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div id="no-count" class="text-3xl font-bold text-red-600">0</div>
                            <div class="text-sm text-gray-600" data-i18n="nobutton.total_nos">Total No's</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div id="streak-count" class="text-3xl font-bold text-orange-600">0</div>
                            <div class="text-sm text-gray-600" data-i18n="nobutton.current_streak">Current Streak</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div id="best-streak" class="text-3xl font-bold text-purple-600">0</div>
                            <div class="text-sm text-gray-600" data-i18n="nobutton.best_streak">Best Streak</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="reset-button" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="nobutton.reset">
                            Reset
                        </button>
                        <button id="practice-mode" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors" data-i18n="nobutton.practice_mode">
                            Practice Mode
                        </button>
                    </div>
                </div>

                <!-- Tips -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nobutton.tip1">Build Confidence</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nobutton.tip2">Set Boundaries</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nobutton.tip3">Practice Assertiveness</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="nobutton.content_title">About The No Button</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="nobutton.content_subtitle">
                    Empower yourself with the confidence to say no. Build healthy boundaries and assertiveness skills.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-red-100">
                        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nobutton.feature1_title">Build Confidence</h3>
                        <p class="text-gray-600" data-i18n="nobutton.feature1_desc">Practice saying no in a safe environment to build confidence for real-life situations.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-orange-100">
                        <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nobutton.feature2_title">Set Boundaries</h3>
                        <p class="text-gray-600" data-i18n="nobutton.feature2_desc">Learn to establish healthy boundaries and protect your time and energy.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nobutton.feature3_title">Track Progress</h3>
                        <p class="text-gray-600" data-i18n="nobutton.feature3_desc">Monitor your progress with confidence metrics and streak tracking.</p>
                    </div>
                </div>

                <!-- Why Saying No Matters -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="nobutton.why_matters">Why Saying No Matters</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="nobutton.benefits_title">Benefits of Saying No</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="nobutton.benefit1">Protects your time and energy</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="nobutton.benefit2">Reduces stress and overwhelm</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="nobutton.benefit3">Builds self-respect and confidence</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="nobutton.benefit4">Improves relationships through honesty</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="nobutton.tips_title">Tips for Saying No</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="nobutton.tip_1">Be direct and honest</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="nobutton.tip_2">Don't over-explain or justify</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="nobutton.tip_3">Offer alternatives when possible</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="nobutton.tip_4">Practice makes it easier</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let noCount = 0;
            let currentStreak = 0;
            let bestStreak = 0;
            let confidence = 0;
            let practiceMode = false;

            // DOM elements
            const noButton = document.getElementById('no-button');
            const resetButton = document.getElementById('reset-button');
            const practiceModeButton = document.getElementById('practice-mode');
            const resultArea = document.getElementById('result-area');
            const noText = document.getElementById('no-text');
            const encouragement = document.getElementById('encouragement');
            const noCountDisplay = document.getElementById('no-count');
            const streakCountDisplay = document.getElementById('streak-count');
            const bestStreakDisplay = document.getElementById('best-streak');
            const confidenceIndicator = document.getElementById('confidence-indicator');
            const confidencePercentage = document.getElementById('confidence-percentage');

            // Encouragement messages
            const encouragementMessages = [
                "Great job! You're building confidence!",
                "Excellent! Setting boundaries is important!",
                "Well done! You're getting stronger!",
                "Perfect! Keep practicing assertiveness!",
                "Amazing! You're learning to protect your time!",
                "Fantastic! Saying no is a valuable skill!",
                "Wonderful! You're building self-respect!",
                "Outstanding! Keep up the great work!",
                "Brilliant! You're mastering boundaries!",
                "Superb! Confidence is growing!"
            ];

            // Practice scenarios
            const practiceScenarios = [
                "A colleague asks you to work overtime on your day off",
                "A friend wants to borrow money you can't afford to lend",
                "Someone asks you to take on extra responsibilities at work",
                "A family member wants you to attend an event you don't want to go to",
                "A salesperson is pressuring you to buy something you don't need",
                "Someone asks you to volunteer for something you don't have time for",
                "A friend wants you to lie for them",
                "Someone asks you to do their work for them"
            ];

            // Update displays
            function updateDisplays() {
                noCountDisplay.textContent = noCount;
                streakCountDisplay.textContent = currentStreak;
                bestStreakDisplay.textContent = bestStreak;
                confidencePercentage.textContent = confidence + '%';
                confidenceIndicator.style.width = confidence + '%';
            }

            // Calculate confidence based on usage
            function updateConfidence() {
                // Confidence increases with usage and streaks
                const baseConfidence = Math.min(noCount * 2, 60);
                const streakBonus = Math.min(currentStreak * 5, 30);
                const practiceBonus = practiceMode ? 10 : 0;

                confidence = Math.min(baseConfidence + streakBonus + practiceBonus, 100);
                updateDisplays();
            }

            // Show result with animation
            function showResult() {
                const randomMessage = encouragementMessages[Math.floor(Math.random() * encouragementMessages.length)];
                encouragement.textContent = randomMessage;

                resultArea.classList.remove('hidden');

                // Hide after 3 seconds
                setTimeout(() => {
                    resultArea.classList.add('hidden');
                }, 3000);
            }

            // Handle no button press
            function pressNoButton() {
                noCount++;
                currentStreak++;

                if (currentStreak > bestStreak) {
                    bestStreak = currentStreak;
                }

                // Add press animation
                noButton.classList.add('pressed');
                setTimeout(() => {
                    noButton.classList.remove('pressed');
                }, 800);

                // Update confidence and displays
                updateConfidence();
                showResult();

                // Add shake effect to stats
                document.querySelector('.grid').classList.add('shake');
                setTimeout(() => {
                    document.querySelector('.grid').classList.remove('shake');
                }, 500);
            }

            // Reset all stats
            function resetStats() {
                noCount = 0;
                currentStreak = 0;
                bestStreak = 0;
                confidence = 0;
                updateDisplays();
                resultArea.classList.add('hidden');
            }

            // Toggle practice mode
            function togglePracticeMode() {
                practiceMode = !practiceMode;

                if (practiceMode) {
                    practiceModeButton.textContent = 'Exit Practice';
                    practiceModeButton.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                    practiceModeButton.classList.add('bg-orange-500', 'hover:bg-orange-600');

                    // Show practice scenario
                    const scenario = practiceScenarios[Math.floor(Math.random() * practiceScenarios.length)];
                    alert(`Practice Scenario: ${scenario}\n\nNow practice saying NO!`);
                } else {
                    practiceModeButton.textContent = 'Practice Mode';
                    practiceModeButton.classList.remove('bg-orange-500', 'hover:bg-orange-600');
                    practiceModeButton.classList.add('bg-blue-500', 'hover:bg-blue-600');
                }

                updateConfidence();
            }

            // Event listeners
            noButton.addEventListener('click', pressNoButton);
            resetButton.addEventListener('click', resetStats);
            practiceModeButton.addEventListener('click', togglePracticeMode);

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.code === 'Space' || e.key === 'n' || e.key === 'N') {
                    e.preventDefault();
                    pressNoButton();
                }

                if (e.key === 'r' || e.key === 'R') {
                    e.preventDefault();
                    resetStats();
                }

                if (e.key === 'p' || e.key === 'P') {
                    e.preventDefault();
                    togglePracticeMode();
                }
            });

            // Initialize displays
            updateDisplays();
        });
    </script>
</body>
</html>