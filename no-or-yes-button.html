<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="No or Yes Button - Simple binary decision tool">
    <title>No or Yes Button | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Button section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="nooryesbutton.title">No or Yes Button</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="flex justify-center space-x-8 mb-8">
                    <button class="bg-red-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-red-700 transition-colors no-button" data-i18n="nooryesbutton.no">NO</button>
                    <button class="bg-green-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-green-700 transition-colors yes-button" data-i18n="nooryesbutton.yes">YES</button>
                </div>
                
                <div class="result-area hidden mt-8">
                    <p class="text-5xl font-bold result-text" style="color: #7c3aed;"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="nooryesbutton.content_title">About The No or Yes Button</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="nooryesbutton.content1">The No or Yes Button provides a straightforward way to make binary decisions.</p>
                
                <p data-i18n="nooryesbutton.content2">This tool reverses the traditional order to help you consider the negative option first before choosing yes.</p>
                
                <p data-i18n="nooryesbutton.content3">Perfect for when you need to carefully weigh both options before making a choice.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const noBtn = document.querySelector('.no-button');
            const yesBtn = document.querySelector('.yes-button');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            noBtn.addEventListener('click', function() {
                resultText.textContent = 'YOU CHOSE NO';
                resultArea.classList.remove('hidden');
            });
            
            yesBtn.addEventListener('click', function() {
                resultText.textContent = 'YOU CHOSE YES';
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>