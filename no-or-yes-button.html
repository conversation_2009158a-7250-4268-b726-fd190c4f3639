<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="No or Yes Button - Reverse order decision maker for thoughtful choices">
    <meta name="keywords" content="no or yes button, reverse decision, thoughtful choice, binary decision">
    <link rel="canonical" href="https://yesnooracle.xyz/no-or-yes-button.html">
    <title>No or Yes Button - Reverse Decision Maker | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .reverse-container {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 25%, #7c3aed 75%, #6d28d9 100%);
            position: relative;
            overflow: hidden;
        }
        .reverse-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="reverse-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="3" fill="white" opacity="0.1"/><circle cx="5" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="25" cy="5" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23reverse-pattern)"/></svg>') repeat;
        }
        .reverse-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .reverse-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            min-width: 160px;
            min-height: 160px;
        }
        .reverse-button:hover {
            transform: scale(1.05);
        }
        .reverse-button:active {
            transform: scale(0.95);
        }
        .reverse-button.pressed {
            animation: reversePress 0.7s ease-out;
        }
        @keyframes reversePress {
            0% { transform: scale(1); }
            25% { transform: scale(0.85); }
            50% { transform: scale(1.15); }
            75% { transform: scale(0.9); }
            100% { transform: scale(1); }
        }
        .reverse-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }
        .reverse-button.pressed::before {
            width: 350px;
            height: 350px;
        }
        .no-first-button {
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.3);
        }
        .no-first-button:hover {
            box-shadow: 0 15px 40px rgba(239, 68, 68, 0.4);
        }
        .yes-second-button {
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }
        .yes-second-button:hover {
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
        }
        .result-display {
            animation: resultFlip 0.8s ease-out;
        }
        @keyframes resultFlip {
            0% { transform: rotateY(90deg); opacity: 0; }
            50% { transform: rotateY(45deg); opacity: 0.5; }
            100% { transform: rotateY(0deg); opacity: 1; }
        }
        .thinking-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .thoughtful-mode {
            background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
            animation: thoughtfulGlow 3s ease-in-out infinite alternate;
        }
        @keyframes thoughtfulGlow {
            from { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
            to { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6); }
        }
        .arrow-divider {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .arrow-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #d1d5db, transparent);
        }
        .arrow-text {
            background: white;
            padding: 0 1rem;
            font-weight: bold;
            color: #6b7280;
            z-index: 1;
        }
        .thinking-timer {
            background: linear-gradient(90deg, #fef3c7 0%, #fbbf24 50%, #f59e0b 100%);
            border-radius: 10px;
            height: 6px;
            position: relative;
            overflow: hidden;
        }
        .timer-indicator {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #dc2626);
            border-radius: 10px;
            transition: width 0.1s linear;
            width: 0%;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 reverse-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="nooryesbutton.title">No or Yes Button</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="nooryesbutton.subtitle">
                    Think twice, choose once. Consider "No" first for more thoughtful decisions.
                </p>

                <!-- Reverse Choice Interface -->
                <div class="max-w-4xl mx-auto reverse-card rounded-xl p-8 mb-8">
                    <!-- Question Input -->
                    <div class="mb-8">
                        <label for="question-input" class="block text-lg font-semibold mb-4" data-i18n="nooryesbutton.question_label">What decision are you considering?</label>
                        <input type="text" id="question-input" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent text-center text-lg" placeholder="Enter your decision here..." data-i18n-placeholder="nooryesbutton.question_placeholder">
                    </div>

                    <!-- Thinking Timer -->
                    <div id="thinking-timer-container" class="hidden mb-8">
                        <h3 class="text-lg font-semibold mb-4" data-i18n="nooryesbutton.thinking_time">Take time to think...</h3>
                        <div class="thinking-timer">
                            <div id="timer-indicator" class="timer-indicator"></div>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600 mt-2">
                            <span data-i18n="nooryesbutton.consider_no">Consider "No" first</span>
                            <span id="timer-text" class="font-semibold">10s</span>
                            <span data-i18n="nooryesbutton.then_decide">Then decide</span>
                        </div>
                    </div>

                    <!-- Reverse Choice Buttons -->
                    <div class="flex flex-col md:flex-row justify-center items-center gap-8 mb-8">
                        <button id="no-button" class="reverse-button no-first-button bg-gradient-to-br from-red-500 to-red-700 text-white rounded-full text-3xl font-bold hover:from-red-600 hover:to-red-800 transition-all duration-300 flex flex-col items-center justify-center">
                            <span class="text-5xl mb-2">✗</span>
                            <span data-i18n="nooryesbutton.no">NO</span>
                            <span class="text-sm opacity-75 mt-1" data-i18n="nooryesbutton.first_option">Consider First</span>
                        </button>

                        <div class="arrow-divider w-full md:w-20">
                            <span class="arrow-text">→</span>
                        </div>

                        <button id="yes-button" class="reverse-button yes-second-button bg-gradient-to-br from-green-500 to-green-700 text-white rounded-full text-3xl font-bold hover:from-green-600 hover:to-green-800 transition-all duration-300 flex flex-col items-center justify-center">
                            <span class="text-5xl mb-2">✓</span>
                            <span data-i18n="nooryesbutton.yes">YES</span>
                            <span class="text-sm opacity-75 mt-1" data-i18n="nooryesbutton.second_option">Then Decide</span>
                        </button>
                    </div>

                    <!-- Result Display -->
                    <div id="result-area" class="hidden mb-8">
                        <div class="result-display bg-gradient-to-r from-purple-100 to-indigo-100 rounded-lg p-6">
                            <div id="choice-result" class="text-4xl font-bold mb-4"></div>
                            <div id="choice-message" class="text-lg text-gray-700"></div>
                            <div id="thinking-feedback" class="text-sm text-gray-600 mt-4 italic"></div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="thinking-stats rounded-lg p-6 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="nooryesbutton.thinking_stats">Thoughtful Decision Statistics</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div id="no-count" class="text-3xl font-bold text-red-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="nooryesbutton.no_choices">No Choices</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="yes-count" class="text-3xl font-bold text-green-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="nooryesbutton.yes_choices">Yes Choices</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div id="avg-thinking-time" class="text-3xl font-bold text-blue-600">0s</div>
                                <div class="text-sm text-gray-600" data-i18n="nooryesbutton.avg_thinking">Avg Thinking Time</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div id="thoughtful-decisions" class="text-3xl font-bold text-purple-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="nooryesbutton.thoughtful_decisions">Thoughtful Decisions</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="reset-stats" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="nooryesbutton.reset">
                            Reset Stats
                        </button>
                        <button id="thoughtful-mode" class="bg-indigo-500 text-white px-6 py-3 rounded-lg hover:bg-indigo-600 transition-colors" data-i18n="nooryesbutton.thoughtful_mode">
                            Thoughtful Mode
                        </button>
                    </div>
                </div>

                <!-- Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nooryesbutton.feature1">Reverse Order</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nooryesbutton.feature2">Thinking Timer</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="nooryesbutton.feature3">Thoughtful Analysis</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="nooryesbutton.content_title">About No or Yes Button</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="nooryesbutton.content_subtitle">
                    Reverse the order, improve the outcome. Consider "No" first for more thoughtful decisions.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-red-100">
                        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nooryesbutton.feature1_title">Consider "No" First</h3>
                        <p class="text-gray-600" data-i18n="nooryesbutton.feature1_desc">By considering rejection first, you evaluate the true necessity and value of saying yes.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-blue-100">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nooryesbutton.feature2_title">Thinking Timer</h3>
                        <p class="text-gray-600" data-i18n="nooryesbutton.feature2_desc">Built-in timer encourages thoughtful consideration before making your final decision.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="nooryesbutton.feature3_title">Decision Analytics</h3>
                        <p class="text-gray-600" data-i18n="nooryesbutton.feature3_desc">Track your decision patterns and thinking time to improve your decision-making process.</p>
                    </div>
                </div>

                <!-- Psychology of Reverse Decision Making -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="nooryesbutton.psychology_title">Psychology of Reverse Decision Making</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="nooryesbutton.benefits_title">Why "No" First Works</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-red-500 mr-2">•</span>
                                    <span data-i18n="nooryesbutton.benefit1">Reduces impulsive yes decisions</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-red-500 mr-2">•</span>
                                    <span data-i18n="nooryesbutton.benefit2">Forces evaluation of true necessity</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-red-500 mr-2">•</span>
                                    <span data-i18n="nooryesbutton.benefit3">Protects time and energy</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-red-500 mr-2">•</span>
                                    <span data-i18n="nooryesbutton.benefit4">Increases decision confidence</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="nooryesbutton.when_to_use">Best Used For</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="nooryesbutton.use1">Important life decisions</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="nooryesbutton.use2">Commitment requests</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="nooryesbutton.use3">Resource allocation choices</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="nooryesbutton.use4">Opportunity evaluation</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let noCount = 0;
            let yesCount = 0;
            let totalThinkingTime = 0;
            let thoughtfulDecisions = 0;
            let thoughtfulMode = false;
            let thinkingStartTime = 0;
            let thinkingTimer = null;

            // DOM elements
            const noButton = document.getElementById('no-button');
            const yesButton = document.getElementById('yes-button');
            const questionInput = document.getElementById('question-input');
            const resultArea = document.getElementById('result-area');
            const choiceResult = document.getElementById('choice-result');
            const choiceMessage = document.getElementById('choice-message');
            const thinkingFeedback = document.getElementById('thinking-feedback');
            const resetStatsButton = document.getElementById('reset-stats');
            const thoughtfulModeButton = document.getElementById('thoughtful-mode');
            const thinkingTimerContainer = document.getElementById('thinking-timer-container');
            const timerIndicator = document.getElementById('timer-indicator');
            const timerText = document.getElementById('timer-text');

            // Statistics elements
            const noCountDisplay = document.getElementById('no-count');
            const yesCountDisplay = document.getElementById('yes-count');
            const avgThinkingTimeDisplay = document.getElementById('avg-thinking-time');
            const thoughtfulDecisionsDisplay = document.getElementById('thoughtful-decisions');

            // Choice messages
            const noMessages = [
                "Wise choice! Sometimes saying no is the best decision.",
                "Good thinking! Protecting your boundaries is important.",
                "Smart decision! Not every opportunity is worth pursuing.",
                "Excellent! You considered the consequences carefully.",
                "Perfect! Quality over quantity in decision making.",
                "Great! You thought about what really matters.",
                "Brilliant! Saying no can be liberating.",
                "Outstanding! You prioritized what's important."
            ];

            const yesMessages = [
                "Great choice! You considered no first and still chose yes.",
                "Excellent! A thoughtful yes is more valuable.",
                "Perfect! You weighed the options carefully.",
                "Wonderful! A deliberate yes shows confidence.",
                "Outstanding! You made a considered decision.",
                "Fantastic! Thoughtful acceptance is powerful.",
                "Brilliant! You chose yes with full awareness.",
                "Amazing! A mindful yes is always better."
            ];

            // Update statistics display
            function updateStatistics() {
                noCountDisplay.textContent = noCount;
                yesCountDisplay.textContent = yesCount;
                thoughtfulDecisionsDisplay.textContent = thoughtfulDecisions;

                const totalDecisions = noCount + yesCount;
                if (totalDecisions > 0) {
                    const avgTime = Math.round(totalThinkingTime / totalDecisions);
                    avgThinkingTimeDisplay.textContent = avgTime + 's';
                } else {
                    avgThinkingTimeDisplay.textContent = '0s';
                }
            }

            // Start thinking timer
            function startThinkingTimer() {
                if (!thoughtfulMode) return;

                thinkingStartTime = Date.now();
                thinkingTimerContainer.classList.remove('hidden');

                let timeLeft = 10;
                timerText.textContent = timeLeft + 's';
                timerIndicator.style.width = '0%';

                thinkingTimer = setInterval(() => {
                    timeLeft--;
                    timerText.textContent = timeLeft + 's';
                    timerIndicator.style.width = ((10 - timeLeft) / 10 * 100) + '%';

                    if (timeLeft <= 0) {
                        clearInterval(thinkingTimer);
                        thinkingTimer = null;
                    }
                }, 1000);
            }

            // Calculate thinking time
            function calculateThinkingTime() {
                if (thinkingStartTime > 0) {
                    const thinkingTime = (Date.now() - thinkingStartTime) / 1000;
                    totalThinkingTime += thinkingTime;

                    if (thinkingTime >= 5) {
                        thoughtfulDecisions++;
                        return Math.round(thinkingTime);
                    }
                }
                return 0;
            }

            // Show result with animation
            function showResult(choice, message, thinkingTime) {
                choiceResult.textContent = choice;
                choiceResult.style.color = choice.includes('NO') ? '#ef4444' : '#10b981';
                choiceMessage.textContent = message;

                if (thinkingTime > 0) {
                    thinkingFeedback.textContent = `You took ${thinkingTime} seconds to consider this decision. Great thoughtfulness!`;
                } else {
                    thinkingFeedback.textContent = 'Quick decision! Consider using Thoughtful Mode for more deliberate choices.';
                }

                resultArea.classList.remove('hidden');

                // Hide timer
                thinkingTimerContainer.classList.add('hidden');

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    resultArea.classList.add('hidden');
                }, 5000);
            }

            // Handle choice selection
            function makeChoice(choice) {
                const question = questionInput.value.trim();
                const thinkingTime = calculateThinkingTime();

                if (choice === 'no') {
                    noCount++;
                    const message = noMessages[Math.floor(Math.random() * noMessages.length)];
                    showResult('✗ NO!', question ? `"${question}" - ${message}` : message, thinkingTime);

                    // Add press animation
                    noButton.classList.add('pressed');
                    setTimeout(() => noButton.classList.remove('pressed'), 700);
                } else {
                    yesCount++;
                    const message = yesMessages[Math.floor(Math.random() * yesMessages.length)];
                    showResult('✓ YES!', question ? `"${question}" - ${message}` : message, thinkingTime);

                    // Add press animation
                    yesButton.classList.add('pressed');
                    setTimeout(() => yesButton.classList.remove('pressed'), 700);
                }

                updateStatistics();

                // Reset for next decision
                thinkingStartTime = 0;
                if (thinkingTimer) {
                    clearInterval(thinkingTimer);
                    thinkingTimer = null;
                }
            }

            // Reset statistics
            function resetStats() {
                noCount = 0;
                yesCount = 0;
                totalThinkingTime = 0;
                thoughtfulDecisions = 0;
                updateStatistics();
                resultArea.classList.add('hidden');
                thinkingTimerContainer.classList.add('hidden');
            }

            // Toggle thoughtful mode
            function toggleThoughtfulMode() {
                thoughtfulMode = !thoughtfulMode;

                if (thoughtfulMode) {
                    thoughtfulModeButton.textContent = 'Exit Thoughtful Mode';
                    thoughtfulModeButton.classList.remove('bg-indigo-500', 'hover:bg-indigo-600');
                    thoughtfulModeButton.classList.add('bg-orange-500', 'hover:bg-orange-600');

                    // Add thoughtful mode styling
                    document.querySelector('.reverse-card').classList.add('thoughtful-mode');

                    questionInput.placeholder = 'Enter your decision and take time to think...';
                } else {
                    thoughtfulModeButton.textContent = 'Thoughtful Mode';
                    thoughtfulModeButton.classList.remove('bg-orange-500', 'hover:bg-orange-600');
                    thoughtfulModeButton.classList.add('bg-indigo-500', 'hover:bg-indigo-600');

                    // Remove thoughtful mode styling
                    document.querySelector('.reverse-card').classList.remove('thoughtful-mode');

                    questionInput.placeholder = 'Enter your decision here...';
                    thinkingTimerContainer.classList.add('hidden');
                }
            }

            // Event listeners
            noButton.addEventListener('click', () => makeChoice('no'));
            yesButton.addEventListener('click', () => makeChoice('yes'));
            resetStatsButton.addEventListener('click', resetStats);
            thoughtfulModeButton.addEventListener('click', toggleThoughtfulMode);

            // Start thinking timer when question is entered in thoughtful mode
            questionInput.addEventListener('input', function() {
                if (thoughtfulMode && this.value.trim() && thinkingStartTime === 0) {
                    startThinkingTimer();
                }
            });

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'n' || e.key === 'N') {
                    e.preventDefault();
                    makeChoice('no');
                }

                if (e.key === 'y' || e.key === 'Y') {
                    e.preventDefault();
                    makeChoice('yes');
                }

                if (e.key === 'r' || e.key === 'R') {
                    e.preventDefault();
                    resetStats();
                }

                if (e.key === 't' || e.key === 'T') {
                    e.preventDefault();
                    toggleThoughtfulMode();
                }

                if (e.key === 'Enter' && questionInput.value.trim()) {
                    questionInput.blur();
                    if (thoughtfulMode && thinkingStartTime === 0) {
                        startThinkingTimer();
                    }
                }
            });

            // Initialize displays
            updateStatistics();
        });
    </script>
</body>
</html>