#!/bin/bash

# YesNoOracle.xyz 部署脚本
# 使用方法: ./deploy.sh [vercel|netlify|server]

set -e

echo "🚀 YesNoOracle.xyz 部署脚本"
echo "================================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "请指定部署方式:"
    echo "  ./deploy.sh vercel   - 部署到Vercel"
    echo "  ./deploy.sh netlify  - 部署到Netlify"
    echo "  ./deploy.sh server   - 部署到自定义服务器"
    exit 1
fi

DEPLOY_TYPE=$1

# 清理不需要的文件
echo "📁 清理不需要的文件..."
rm -f *.xlsx
rm -f UPDATE-PROGRESS.md
rm -f README-HTML-INTEGRATION.md
rm -f DEPLOYMENT-GUIDE.md

# 验证必需文件
echo "✅ 验证必需文件..."
required_files=(
    "index.html"
    "button-maker.html"
    "yes-no-button.html"
    "yes-no-oracle.html"
    "yes-no-oracle-accurate.html"
    "yesno.html"
    "no-button.html"
    "button-clicker.html"
    "game-buzzers.html"
    "yes-and-no-button.html"
    "no-or-yes-button.html"
    "yes-or-no-tarot-accurate.html"
    "yes-or-no-tarot-wheel.html"
    "yes-or-mo.html"
    "src/styles/shared-styles.css"
    "src/js/shared-functions.js"
    "robots.txt"
    "sitemap.xml"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必需文件: $file"
        exit 1
    fi
done

echo "✅ 所有必需文件都存在"

# 根据部署类型执行相应操作
case $DEPLOY_TYPE in
    "vercel")
        echo "🌐 部署到Vercel..."
        
        # 检查Vercel CLI
        if ! command -v vercel &> /dev/null; then
            echo "📦 安装Vercel CLI..."
            npm install -g vercel
        fi
        
        # 部署
        echo "🚀 开始部署..."
        vercel --prod
        
        echo "✅ Vercel部署完成!"
        echo "🌍 访问: https://yesnooracle.xyz"
        ;;
        
    "netlify")
        echo "🌐 部署到Netlify..."
        
        # 检查Netlify CLI
        if ! command -v netlify &> /dev/null; then
            echo "📦 安装Netlify CLI..."
            npm install -g netlify-cli
        fi
        
        # 部署
        echo "🚀 开始部署..."
        netlify deploy --prod --dir .
        
        echo "✅ Netlify部署完成!"
        echo "🌍 访问: https://yesnooracle.xyz"
        ;;
        
    "server")
        echo "🖥️  部署到自定义服务器..."
        
        # 检查服务器配置
        if [ -z "$SERVER_HOST" ] || [ -z "$SERVER_USER" ] || [ -z "$SERVER_PATH" ]; then
            echo "❌ 请设置环境变量:"
            echo "   export SERVER_HOST=your-server-ip"
            echo "   export SERVER_USER=your-username"
            echo "   export SERVER_PATH=/var/www/yesnooracle"
            exit 1
        fi
        
        # 上传文件
        echo "📤 上传文件到服务器..."
        rsync -avz --delete \
            --exclude='*.md' \
            --exclude='*.xlsx' \
            --exclude='deploy.sh' \
            --exclude='.git' \
            ./ $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
        
        # 设置权限
        echo "🔐 设置文件权限..."
        ssh $SERVER_USER@$SERVER_HOST "sudo chown -R www-data:www-data $SERVER_PATH && sudo chmod -R 755 $SERVER_PATH"
        
        echo "✅ 服务器部署完成!"
        echo "🌍 访问: https://yesnooracle.xyz"
        ;;
        
    *)
        echo "❌ 未知的部署类型: $DEPLOY_TYPE"
        echo "支持的类型: vercel, netlify, server"
        exit 1
        ;;
esac

echo ""
echo "🎉 部署完成!"
echo "📊 建议接下来:"
echo "   1. 验证所有页面都能正常访问"
echo "   2. 检查SSL证书是否正常工作"
echo "   3. 测试移动端响应式设计"
echo "   4. 设置Google Analytics和Search Console"
echo "   5. 提交sitemap到搜索引擎"
