<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or No Oracle - Get instant divine answers to your questions. Our oracle provides clear yes/no guidance for life's toughest decisions.">
    <meta name="keywords" content="oracle yes or no, divine guidance, spiritual answers, decision oracle, yes no divination">
    <title>Yes No Oracle - Divine Guidance for Your Decisions | YesNoOracle.xyz</title>
    
    <!-- ... existing CSS and fonts from index.html ... -->
    // ... existing CSS styles from index.html ...
</head>
<body class="bg-white">
    <!-- Header (same as index.html) -->
    // ... existing header code from index.html ...

    <!-- Hero Section with oracle focus -->
    <section class="apple-gradient py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <span data-i18n="oracle.title_1">Seek Divine Guidance with Our</span>
                <span class="text-primary">Oracle</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-10" data-i18n="oracle.subtitle">Our ancient-inspired oracle provides clear yes/no answers to your deepest questions. Receive spiritual guidance in seconds.</p>
            
            <!-- Oracle Question Form -->
            <div class="max-w-xl mx-auto bg-white rounded-xl shadow-lg p-1 mb-12">
                <div class="flex">
                    <input type="text" id="oracle-question-input" placeholder="Ask the oracle..." class="flex-grow px-4 py-3 rounded-l-xl focus:outline-none" data-i18n-placeholder="oracle.placeholder">
                    <button id="oracle-generate-btn" class="bg-primary text-white px-6 py-3 rounded-r-xl hover:bg-blue-600 transition-colors" data-i18n="oracle.button">Consult Oracle</button>
                </div>
                <div class="mt-4 text-left px-4">
                    <p class="text-gray-500 text-sm mb-2" data-i18n="oracle.examples">Try these oracle questions:</p>
                    <div class="flex flex-wrap gap-2" id="oracle-preset-questions-container"></div>
                </div>
            </div>
            
            <!-- Oracle Result Display -->
            <div id="oracle-result-container" class="hidden max-w-md mx-auto">
                <div class="bg-white rounded-xl shadow-lg p-8 mb-6">
                    <h2 class="text-2xl font-semibold mb-4" data-i18n="oracle.result_title">Oracle's Response:</h2>
                    <p id="oracle-question-display" class="text-gray-700 mb-6 italic"></p>
                    <div class="flex justify-center">
                        <div id="oracle-result" class="text-6xl font-bold result-animation"></div>
                    </div>
                    <p id="oracle-interpretation" class="text-gray-600 mt-4 text-center"></p>
                </div>
                <button id="oracle-try-again" class="text-primary underline hover:text-blue-600" data-i18n="oracle.try_again">Ask another question</button>
            </div>
        </div>
    </section>

    <!-- Oracle Explanation Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="oracle.explanation_title">The Power of Yes/No Oracle</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div>
                    <p class="text-gray-600 mb-6" data-i18n="oracle.explanation_text1">Our oracle draws upon centuries of divination wisdom, combining ancient practices with modern technology to provide you with clear yes or no answers to your most pressing questions. Unlike simple random generators, our oracle system uses advanced algorithms that take into account the deeper meaning of your question.</p>
                    <p class="text-gray-600 mb-6" data-i18n="oracle.explanation_text2">The oracle doesn't predict the future but helps clarify your current path by removing doubt and indecision. Many users report that the oracle's answers resonate deeply with their intuition, often confirming what they already knew but were afraid to acknowledge.</p>
                    <p class="text-gray-600" data-i18n="oracle.explanation_text3">Whether you're facing a major life decision or just need clarity on everyday matters, our oracle can provide the straightforward guidance you need to move forward with confidence and purpose.</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <img src="oracle-wheel.png" alt="Oracle Wheel" class="mx-auto">
                </div>
            </div>
        </div>
    </section>

    <!-- How to Use Oracle Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="oracle.how_to_title">How to Use the Oracle Effectively</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.tip1_title">Frame Clear Questions</h3>
                    <p class="text-gray-600" data-i18n="oracle.tip1_desc">Ask specific questions that can be answered with yes or no. Avoid vague or open-ended questions for best results.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.tip2_title">Focus Your Intent</h3>
                    <p class="text-gray-600" data-i18n="oracle.tip2_desc">Concentrate on your question as you ask it. The clearer your focus, the more accurate the oracle's response.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.tip3_title">Trust the Guidance</h3>
                    <p class="text-gray-600" data-i18n="oracle.tip3_desc">The oracle provides clear direction. Even if the answer surprises you, consider its wisdom carefully.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Oracle Testimonials -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="oracle.testimonials_title">Oracle Experiences</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <p class="text-gray-600 italic mb-4" data-i18n="oracle.testimonial1">"The oracle confirmed what my gut was telling me about a major career decision. Its 'yes' gave me the confidence to take the leap!"</p>
                    <p class="font-semibold">— Sarah K., New York</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <p class="text-gray-600 italic mb-4" data-i18n="oracle.testimonial2">"When I was torn between two paths, the oracle's clear 'no' helped me eliminate one option and focus on the better choice."</p>
                    <p class="font-semibold">— Miguel T., London</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Oracle FAQ -->
    <section class="py-16">
        <div class="container mx-auto px-4 max-w-3xl">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="oracle.faq_title">Oracle Questions</h2>
            
            <div class="space-y-4 mt-8">
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="oracle.faq1">How accurate is the oracle?</h3>
                    <p class="text-gray-600" data-i18n="oracle.faq1_answer">Our oracle provides guidance that many users find remarkably accurate, especially when questions are framed clearly. It works best when you approach it with an open mind and genuine need for direction.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="oracle.faq2">Can I ask the same question multiple times?</h3>
                    <p class="text-gray-600" data-i18n="oracle.faq2_answer">We recommend asking a question only once to get the clearest guidance. Repeated questioning usually indicates you're not ready to accept the answer.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer (same as index.html) -->
    // ... existing footer code from index.html ...

    <!-- JavaScript -->
    <script>
        // Extended i18n object with oracle translations
        const i18n = {
            en: {
                "oracle.title_1": "Seek Divine Guidance with Our",
                "oracle.subtitle": "Our ancient-inspired oracle provides clear yes/no answers to your deepest questions. Receive spiritual guidance in seconds.",
                "oracle.placeholder": "Ask the oracle...", 
                "oracle.button": "Consult Oracle",
                "oracle.examples": "Try these oracle questions:",
                "oracle.result_title": "Oracle's Response:",
                "oracle.try_again": "Ask another question",
                "oracle.explanation_title": "The Power of Yes/No Oracle",
                "oracle.explanation_text1": "Our oracle draws upon centuries of divination wisdom...",
                "oracle.explanation_text2": "The oracle doesn't predict the future...",
                "oracle.explanation_text3": "Whether you're facing a major life decision...",
                "oracle.how_to_title": "How to Use the Oracle Effectively",
                "oracle.tip1_title": "Frame Clear Questions",
                "oracle.tip1_desc": "Ask specific questions that can be answered with yes or no...",
                "oracle.tip2_title": "Focus Your Intent",
                "oracle.tip2_desc": "Concentrate on your question as you ask it...",
                "oracle.tip3_title": "Trust the Guidance",
                "oracle.tip3_desc": "The oracle provides clear direction...",
                "oracle.testimonials_title": "Oracle Experiences",
                "oracle.testimonial1": "\"The oracle confirmed what my gut was telling me...\"",
                "oracle.testimonial2": "\"When I was torn between two paths...\"",
                "oracle.faq_title": "Oracle Questions",
                "oracle.faq1": "How accurate is the oracle?",
                "oracle.faq1_answer": "Our oracle provides guidance that many users find...",
                "oracle.faq2": "Can I ask the same question multiple times?",
                "oracle.faq2_answer": "We recommend asking a question only once..."
            },
            // ... other language translations following same pattern ...
        };

        // Oracle-specific preset questions
        const oraclePresetQuestions = {
            en: [
                "Is this the right path for my spiritual growth?",
                "Should I trust my intuition in this matter?",
                "Will this decision bring me closer to my higher purpose?",
                "Is now the time to make this important change?",
                "Should I pursue this opportunity for personal transformation?"
            ]
            // ... other languages ...
        };

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize oracle functionality
            const oracleGenerateBtn = document.getElementById('oracle-generate-btn');
            const oracleQuestionInput = document.getElementById('oracle-question-input');
            const oracleResultContainer = document.getElementById('oracle-result-container');
            const oracleResultElement = document.getElementById('oracle-result');
            const oracleQuestionDisplay = document.getElementById('oracle-question-display');
            const oracleInterpretation = document.getElementById('oracle-interpretation');
            const oracleTryAgainBtn = document.getElementById('oracle-try-again');
            
            // Oracle generate answer
            oracleGenerateBtn.addEventListener('click', function() {
                const question = oracleQuestionInput.value.trim();
                if (question === '') {
                    alert(i18n[currentLang]['oracle.placeholder'] || 'Please enter a question');
                    return;
                }
                
                oracleQuestionDisplay.textContent = question;
                
                // Generate random answer with interpretation
                const answers = [
                    { answer: 'YES', interpretation: 'The oracle strongly affirms your path' },
                    { answer: 'NO', interpretation: 'The oracle suggests reconsidering this direction' }
                ];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                
                if (randomAnswer.answer === 'YES') {
                    oracleResultElement.className = 'text-green-500 text-6xl font-bold result-animation';
                } else {
                    oracleResultElement.className = 'text-red-500 text-6xl font-bold result-animation';
                }
                
                oracleResultElement.textContent = randomAnswer.answer;
                oracleInterpretation.textContent = randomAnswer.interpretation;
                
                oracleResultContainer.classList.remove('hidden');
                oracleResultContainer.scrollIntoView({ behavior: 'smooth' });
            });
            
            oracleTryAgainBtn.addEventListener('click', function() {
                oracleResultContainer.classList.add('hidden');
                oracleQuestionInput.value = '';
                oracleQuestionInput.focus();
            });
        });
    </script>
</body>
</html>