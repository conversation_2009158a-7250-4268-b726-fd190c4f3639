[build]
  publish = "."
  command = ""

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/src/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
  from = "/oracle-page.html"
  to = "/yes-no-oracle.html"
  status = 301

[[redirects]]
  from = "/yes-and-no-oracle.html"
  to = "/yes-no-oracle.html"
  status = 301

# Redirect www to non-www
[[redirects]]
  from = "https://www.yesnooracle.xyz/*"
  to = "https://yesnooracle.xyz/:splat"
  status = 301
  force = true
