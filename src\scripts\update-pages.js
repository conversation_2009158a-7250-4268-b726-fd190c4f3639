// <PERSON>ript to help update HTML pages with shared components
// This is a helper script for developers to understand the update process

const pageConfigurations = {
    'button-maker.html': {
        title: 'Button Maker - Create Custom Buttons Online',
        description: 'Free online Button Maker tool - Create custom buttons in seconds',
        keywords: 'button maker, custom buttons, online tool, web design, UI elements',
        category: 'Tools'
    },
    'yes-no-button.html': {
        title: 'Yes No Button - Simple Decision Maker',
        description: 'Quick yes or no decisions with our simple button tool',
        keywords: 'yes no button, decision maker, simple choice, quick decision',
        category: 'Tools'
    },
    'yes-no-oracle.html': {
        title: 'Yes No Oracle - Divine Guidance Tool',
        description: 'Free Yes No Oracle - Get divine guidance for your questions with our ancient oracle system',
        keywords: 'yes no oracle, divine guidance, spiritual answers, oracle questions, free oracle',
        category: 'Tools'
    },
    'yes-no-oracle-accurate.html': {
        title: 'Yes No Oracle Accurate - Precise Spiritual Guidance',
        description: 'Get accurate yes or no answers with our enhanced oracle system',
        keywords: 'accurate oracle, precise guidance, spiritual answers, yes no accurate',
        category: 'Tools'
    },
    'yesno.html': {
        title: 'YesNo - Quick Decision Tool',
        description: 'Simple yes or no decision maker for quick choices',
        keywords: 'yesno, quick decision, simple choice, decision tool',
        category: 'Tools'
    },
    'no-button.html': {
        title: 'No Button - Negative Decision Tool',
        description: 'Sometimes you need to say no - use our no button tool',
        keywords: 'no button, negative decision, rejection tool, say no',
        category: 'Tools'
    },
    'button-clicker.html': {
        title: 'Button Clicker - Interactive Clicking Game',
        description: 'Fun button clicking game and interactive tool',
        keywords: 'button clicker, clicking game, interactive button, fun tool',
        category: 'Tools'
    },
    'game-buzzers.html': {
        title: 'Game Buzzers - Quiz Show Buzzers',
        description: 'Virtual game show buzzers for quizzes and competitions',
        keywords: 'game buzzers, quiz buzzers, game show, competition tool',
        category: 'Tools'
    },
    'yes-and-no-button.html': {
        title: 'Yes And No Button - Dual Choice Tool',
        description: 'Make decisions with both yes and no options available',
        keywords: 'yes and no button, dual choice, decision tool, both options',
        category: 'Tools'
    },
    'no-or-yes-button.html': {
        title: 'No Or Yes Button - Alternative Decision Tool',
        description: 'Alternative approach to yes/no decisions',
        keywords: 'no or yes button, alternative decision, choice tool, decision maker',
        category: 'Tools'
    },
    'yes-or-no-tarot-accurate.html': {
        title: 'Yes Or No Tarot Accurate - Precise Tarot Guidance',
        description: 'Accurate tarot-based yes or no readings for spiritual guidance',
        keywords: 'tarot accurate, yes no tarot, spiritual guidance, tarot reading',
        category: 'Tools'
    },
    'yes-or-no-tarot-wheel.html': {
        title: 'Yes Or No Tarot Wheel - Spinning Tarot Oracle',
        description: 'Interactive tarot wheel for yes or no spiritual guidance',
        keywords: 'tarot wheel, spinning oracle, tarot guidance, interactive tarot',
        category: 'Tools'
    },
    'yes-or-mo.html': {
        title: 'Yes Or Mo - Decision Variation Tool',
        description: 'Unique variation of yes or no decision making',
        keywords: 'yes or mo, decision variation, unique choice, alternative decision',
        category: 'Tools'
    }
};

// Template for updating HTML files
const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{{DESCRIPTION}}">
    <meta name="keywords" content="{{KEYWORDS}}">
    <link rel="canonical" href="https://yesnooracle.xyz/{{FILENAME}}">
    <title>{{TITLE}} | YesNoOracle.xyz</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">
    
    <!-- Page-specific styles -->
    <style>
        {{PAGE_STYLES}}
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>
    
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        {{PAGE_CONTENT}}
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>
    
    <!-- Page-specific JavaScript -->
    <script>
        {{PAGE_JAVASCRIPT}}
    </script>
</body>
</html>`;

// Function to generate updated HTML for a specific page
function generateUpdatedHTML(filename) {
    const config = pageConfigurations[filename];
    if (!config) {
        console.log(`No configuration found for ${filename}`);
        return null;
    }
    
    return htmlTemplate
        .replace('{{TITLE}}', config.title)
        .replace('{{DESCRIPTION}}', config.description)
        .replace('{{KEYWORDS}}', config.keywords)
        .replace('{{FILENAME}}', filename);
}

// Export configurations for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        pageConfigurations,
        htmlTemplate,
        generateUpdatedHTML
    };
}

console.log('Page update helper script loaded. Use generateUpdatedHTML(filename) to get updated HTML for a page.');
