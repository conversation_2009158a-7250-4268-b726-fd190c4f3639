<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Struggling with decisions? Get instant yes or no answers to your questions. Free, simple, and trusted by thousands.">
    <meta name="keywords" content="yes or no, decision maker, random decision, choice helper, oracle, yes no generator">
    <link rel="canonical" href="https://yesnooracle.xyz">
    <title>Yes or No Oracle - Instant Decision Maker | YesNoOracle.xyz</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-font-smoothing: antialiased;
            color: #1d1d1f;
        }
        .apple-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
        }
        .result-animation {
            animation: pulse 0.5s ease-in-out;
        }
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        .preset-question:hover {
            transform: translateY(-2px);
            transition: all 0.2s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }
        .language-selector {
            background: transparent;
            border: none;
            padding: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .language-selector:focus {
            outline: none;
            background: rgba(0, 113, 227, 0.1);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <span class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></span>
            </div>
            <nav class="hidden md:flex space-x-8">
    <div class="relative group">
        <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
            Tools
            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </button>
        <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
            <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
            <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
            <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
            <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
            <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
            <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
            <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
            <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
            <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
            <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
            <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
            <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
            <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
        </div>
    </div>
                <a href="#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <button class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="apple-gradient py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <span data-i18n="hero.title_1">Can't Decide? Get an Instant</span> 
                <span class="text-primary">Yes</span> 
                <span data-i18n="hero.title_2">or</span> 
                <span class="text-accent">No</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-10" data-i18n="hero.subtitle">Thousands use our oracle daily for quick, unbiased answers to life's tough questions. Free and reliable.</p>
            
            <div class="max-w-xl mx-auto bg-white rounded-xl shadow-lg p-1 mb-12">
                <div class="flex">
                    <input type="text" id="question-input" placeholder="Ask your question..." class="flex-grow px-4 py-3 rounded-l-xl focus:outline-none" data-i18n-placeholder="hero.placeholder">
                    <button id="generate-btn" class="bg-primary text-white px-6 py-3 rounded-r-xl hover:bg-blue-600 transition-colors" data-i18n="hero.button">Get Answer</button>
                </div>
                <div class="mt-4 text-left px-4">
                    <p class="text-gray-500 text-sm mb-2" data-i18n="hero.examples">Try these examples:</p>
                    <div class="flex flex-wrap gap-2" id="preset-questions-container">
                        <!-- Preset questions will be generated by JS -->
                    </div>
                </div>
            </div>
            
            <div id="result-container" class="hidden max-w-md mx-auto">
                <div class="bg-white rounded-xl shadow-lg p-8 mb-6">
                    <h2 class="text-2xl font-semibold mb-4" data-i18n="result.your_question">Your Question:</h2>
                    <p id="question-display" class="text-gray-700 mb-6 italic"></p>
                    <div class="flex justify-center">
                        <div id="result" class="text-6xl font-bold result-animation"></div>
                    </div>
                </div>
                <button id="try-again" class="text-primary underline hover:text-blue-600" data-i18n="result.try_again">Ask another question</button>
            </div>
            
            <div class="mt-12 flex justify-center space-x-4">
                <div class="flex items-center text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span data-i18n="stats.decisions">5,000+ Daily Decisions</span>
                </div>
                <div class="flex items-center text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span data-i18n="stats.satisfaction">93% User Satisfaction</span>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="how_it_works.title">How YesNoOracle Works</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="how_it_works.subtitle">Our decision-making algorithm provides unbiased answers in seconds. Here's the simple process:</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step1_title">Ask Your Question</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step1_desc">Formulate your dilemma clearly. The more specific your question, the better the guidance.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step2_title">Generate Answer</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step2_desc">Our algorithm processes your query using advanced decision-making frameworks.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step3_title">Receive Clear Direction</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step3_desc">Get an immediate yes or no answer, helping you move forward with confidence.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section id="features" class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="features.title">Powerful Features</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="features.subtitle">Why thousands trust YesNoOracle for their daily decisions</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature1_title">Instant Answers</h3>
                        <p class="text-gray-600" data-i18n="features.feature1_desc">No more overthinking. Get clear yes or no answers in seconds, saving you time and mental energy.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature2_title">Private & Secure</h3>
                        <p class="text-gray-600" data-i18n="features.feature2_desc">Your questions remain confidential. We don't store personal data or decision history.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature3_title">Decision History</h3>
                        <p class="text-gray-600" data-i18n="features.feature3_desc">Premium users can save and revisit past decisions to track their choice patterns.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature4_title">Lightning Fast</h3>
                        <p class="text-gray-600" data-i18n="features.feature4_desc">Optimized for speed. Get answers instantly without any unnecessary delays.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section id="testimonials" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="testimonials.title">Trusted by Thousands</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="testimonials.subtitle">Real people making better decisions every day</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                            <span class="font-bold">ES</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Emma S.</h4>
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 italic" data-i18n="testimonials.testimonial1">"I used to waste hours deciding small things. Now I get instant clarity. This tool has saved me so much time!"</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                            <span class="font-bold">MJ</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Miguel J.</h4>
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 italic" data-i18n="testimonials.testimonial2">"As a manager, I make dozens of decisions daily. YesNoOracle helps me clear mental blocks and move forward confidently."</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                            <span class="font-bold">AT</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Aisha T.</h4>
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 italic" data-i18n="testimonials.testimonial3">"I was skeptical at first, but the answers I get often align with my intuition. Great for breaking decision paralysis!"</p>
                </div>
            </div>
            
            <div class="mt-12 text-center">
                <div class="inline-flex items-center">
                    <div class="flex -space-x-2">
                        <div class="w-10 h-10 rounded-full bg-gray-200 border-2 border-white"></div>
                        <div class="w-10 h-10 rounded-full bg-gray-200 border-2 border-white"></div>
                        <div class="w-10 h-10 rounded-full bg-gray-200 border-2 border-white"></div>
                        <div class="w-10 h-10 rounded-full bg-gray-200 border-2 border-white"></div>
                        <div class="w-10 h-10 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-sm font-bold">5K+</div>
                    </div>
                    <span class="ml-4 text-gray-600" data-i18n="testimonials.users">Active monthly users</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing -->
    <section id="pricing" class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="pricing.title">Simple, Transparent Pricing</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="pricing.subtitle">Free forever, with optional premium features</p>
            
            <div class="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white border border-gray-200 rounded-xl p-8">
                    <h3 class="text-2xl font-bold text-center mb-4" data-i18n="pricing.free_title">Free Plan</h3>
                    <div class="text-center mb-6">
                        <span class="text-4xl font-bold">$0</span>
                        <span class="text-gray-600" data-i18n="pricing.forever">/forever</span>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.free_feature1">Unlimited Yes/No questions</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.free_feature2">Instant answers</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.free_feature3">10 preset questions</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            <span data-i18n="pricing.free_feature4">Decision history</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            <span data-i18n="pricing.free_feature5">Advanced insights</span>
                        </li>
                    </ul>
                    <button class="w-full bg-gray-100 text-gray-800 py-3 rounded-lg font-medium" data-i18n="pricing.free_button">Start Free</button>
                </div>
                
                <div class="bg-white border-2 border-primary rounded-xl p-8 relative overflow-hidden">
                    <div class="absolute top-0 right-0 bg-primary text-white px-4 py-1 text-sm font-bold rounded-bl-lg" data-i18n="pricing.popular">POPULAR</div>
                    <h3 class="text-2xl font-bold text-center mb-4" data-i18n="pricing.premium_title">Premium Plan</h3>
                    <div class="text-center mb-6">
                        <span class="text-4xl font-bold">$3</span>
                        <span class="text-gray-600" data-i18n="pricing.per_month">/month</span>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.premium_feature1">Everything in Free</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.premium_feature2">Unlimited decision history</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.premium_feature3">Detailed decision insights</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.premium_feature4">Priority support</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span data-i18n="pricing.premium_feature5">Ad-free experience</span>
                        </li>
                    </ul>
                    <button class="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors" data-i18n="pricing.premium_button">Get Premium</button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section id="faq" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 max-w-3xl">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="faq.title">Frequently Asked Questions</h2>
            <p class="text-gray-600 text-center mb-12" data-i18n="faq.subtitle">Everything you need to know about YesNoOracle</p>
            
            <div class="space-y-4">
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="faq.question1">Is YesNoOracle really free?</h3>
                    <p class="text-gray-600" data-i18n="faq.answer1">Yes! Our core decision-making functionality is completely free to use forever. We offer a premium plan with additional features, but you can always get yes/no answers without paying.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="faq.question2">How does the decision algorithm work?</h3>
                    <p class="text-gray-600" data-i18n="faq.answer2">Our proprietary algorithm uses advanced randomization techniques combined with decision science principles to provide unbiased answers. The process is designed to eliminate human bias from your decision-making.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="faq.question3">Can I save my decision history?</h3>
                    <p class="text-gray-600" data-i18n="faq.answer3">Yes, with our Premium plan you can save and revisit all your past decisions. This feature helps you track patterns in your decision-making over time.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="faq.question4">Is this service available in multiple languages?</h3>
                    <p class="text-gray-600" data-i18n="faq.answer4">Yes! We currently support English, Spanish, Chinese, Japanese, and Korean. You can switch languages using the dropdown in the top navigation.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl">
                    <h3 class="font-semibold text-lg mb-2" data-i18n="faq.question5">What types of questions work best?</h3>
                    <p class="text-gray-600" data-i18n="faq.answer5">YesNoOracle works best for binary decisions - questions that can be answered with a simple "yes" or "no". For complex decisions with multiple factors, we recommend breaking them down into smaller yes/no questions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA -->
    <section class="py-16 bg-primary text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4" data-i18n="cta.title">Stop Overthinking. Start Deciding.</h2>
            <p class="text-blue-100 max-w-2xl mx-auto mb-8" data-i18n="cta.subtitle">Join thousands who have found clarity with our simple decision-making tool.</p>
            <button class="bg-white text-primary px-8 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors" data-i18n="cta.button">Try YesNoOracle Free</button>
            <p class="mt-4 text-blue-100" data-i18n="cta.note">No credit card required. Free forever.</p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm3 8h-1.35c-.538 0-.65.221-.65.778v1.222h2l-.209 2h-1.791v7h-3v-7h-2v-2h2v-2.308c0-1.769.931-2.692 3.029-2.692h1.971v3z"/></svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.download">Download</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.careers">Careers</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.press">Press</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.privacy">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Multilingual content
        const i18n = {
            en: {
                // Navigation
                "nav.how_it_works": "How It Works",
                "nav.features": "Features",
                "nav.testimonials": "Testimonials",
                "nav.pricing": "Pricing",
                "nav.faq": "FAQ",
                "nav.try_free": "Try Free",
                
                // Hero section
                "hero.title_1": "Can't Decide? Get an Instant",
                "hero.title_2": "or",
                "hero.subtitle": "Thousands use our oracle daily for quick, unbiased answers to life's tough questions. Free and reliable.",
                "hero.placeholder": "Ask your question...",
                "hero.button": "Get Answer",
                "hero.examples": "Try these examples:",
                
                // Stats
                "stats.decisions": "5,000+ Daily Decisions",
                "stats.satisfaction": "93% User Satisfaction",
                
                // How It Works
                "how_it_works.title": "How YesNoOracle Works",
                "how_it_works.subtitle": "Our decision-making algorithm provides unbiased answers in seconds. Here's the simple process:",
                "how_it_works.step1_title": "Ask Your Question",
                "how_it_works.step1_desc": "Formulate your dilemma clearly. The more specific your question, the better the guidance.",
                "how_it_works.step2_title": "Generate Answer",
                "how_it_works.step2_desc": "Our algorithm processes your query using advanced decision-making frameworks.",
                "how_it_works.step3_title": "Receive Clear Direction",
                "how_it_works.step3_desc": "Get an immediate yes or no answer, helping you move forward with confidence.",
                
                // Features
                "features.title": "Powerful Features",
                "features.subtitle": "Why thousands trust YesNoOracle for their daily decisions",
                "features.feature1_title": "Instant Answers",
                "features.feature1_desc": "No more overthinking. Get clear yes or no answers in seconds, saving you time and mental energy.",
                "features.feature2_title": "Private & Secure",
                "features.feature2_desc": "Your questions remain confidential. We don't store personal data or decision history.",
                "features.feature3_title": "Decision History",
                "features.feature3_desc": "Premium users can save and revisit past decisions to track their choice patterns.",
                "features.feature4_title": "Lightning Fast",
                "features.feature4_desc": "Optimized for speed. Get answers instantly without any unnecessary delays.",
                
                // Testimonials
                "testimonials.title": "Trusted by Thousands",
                "testimonials.subtitle": "Real people making better decisions every day",
                "testimonials.testimonial1": "\"I used to waste hours deciding small things. Now I get instant clarity. This tool has saved me so much time!\"",
                "testimonials.testimonial2": "\"As a manager, I make dozens of decisions daily. YesNoOracle helps me clear mental blocks and move forward confidently.\"",
                "testimonials.testimonial3": "\"I was skeptical at first, but the answers I get often align with my intuition. Great for breaking decision paralysis!\"",
                "testimonials.users": "Active monthly users",
                
                // Pricing
                "pricing.title": "Simple, Transparent Pricing",
                "pricing.subtitle": "Free forever, with optional premium features",
                "pricing.free_title": "Free Plan",
                "pricing.forever": "/forever",
                "pricing.free_feature1": "Unlimited Yes/No questions",
                "pricing.free_feature2": "Instant answers",
                "pricing.free_feature3": "10 preset questions",
                "pricing.free_feature4": "Decision history",
                "pricing.free_feature5": "Advanced insights",
                "pricing.free_button": "Start Free",
                "pricing.popular": "POPULAR",
                "pricing.premium_title": "Premium Plan",
                "pricing.per_month": "/month",
                "pricing.premium_feature1": "Everything in Free",
                "pricing.premium_feature2": "Unlimited decision history",
                "pricing.premium_feature3": "Detailed decision insights",
                "pricing.premium_feature4": "Priority support",
                "pricing.premium_feature5": "Ad-free experience",
                "pricing.premium_button": "Get Premium",
                
                // FAQ
                "faq.title": "Frequently Asked Questions",
                "faq.subtitle": "Everything you need to know about YesNoOracle",
                "faq.question1": "Is YesNoOracle really free?",
                "faq.answer1": "Yes! Our core decision-making functionality is completely free to use forever. We offer a premium plan with additional features, but you can always get yes/no answers without paying.",
                "faq.question2": "How does the decision algorithm work?",
                "faq.answer2": "Our proprietary algorithm uses advanced randomization techniques combined with decision science principles to provide unbiased answers. The process is designed to eliminate human bias from your decision-making.",
                "faq.question3": "Can I save my decision history?",
                "faq.answer3": "Yes, with our Premium plan you can save and revisit all your past decisions. This feature helps you track patterns in your decision-making over time.",
                "faq.question4": "Is this service available in multiple languages?",
                "faq.answer4": "Yes! We currently support English, Spanish, Chinese, Japanese, and Korean. You can switch languages using the dropdown in the top navigation.",
                "faq.question5": "What types of questions work best?",
                "faq.answer5": "YesNoOracle works best for binary decisions - questions that can be answered with a simple \"yes\" or \"no\". For complex decisions with multiple factors, we recommend breaking them down into smaller yes/no questions.",
                
                // CTA
                "cta.title": "Stop Overthinking. Start Deciding.",
                "cta.subtitle": "Join thousands who have found clarity with our simple decision-making tool.",
                "cta.button": "Try YesNoOracle Free",
                "cta.note": "No credit card required. Free forever.",
                
                // Footer
                "footer.description": "Instant decision-making for the indecisive. Free, simple, reliable.",
                "footer.product": "Product",
                "footer.how_it_works": "How It Works",
                "footer.features": "Features",
                "footer.pricing": "Pricing",
                "footer.download": "Download",
                "footer.company": "Company",
                "footer.about": "About Us",
                "footer.careers": "Careers",
                "footer.blog": "Blog",
                "footer.press": "Press",
                "footer.support": "Support",
                "footer.faq": "FAQ",
                "footer.contact": "Contact",
                "footer.privacy": "Privacy Policy",
                "footer.terms": "Terms of Service",
                "footer.rights": "All rights reserved.",
                "footer.created_by": "Created by",
                "footer.contact_label": "Contact:",
                
                // Result
                "result.your_question": "Your Question:",
                "result.try_again": "Ask another question"
            },
            zh: {
                // Navigation
                "nav.how_it_works": "工作原理",
                "nav.features": "功能特点",
                "nav.testimonials": "用户评价",
                "nav.pricing": "价格方案",
                "nav.faq": "常见问题",
                "nav.try_free": "免费试用",
                
                // Hero section
                "hero.title_1": "难以决定？立即获取",
                "hero.title_2": "或",
                "hero.subtitle": "每天有数千人使用我们的决策工具，快速获得生活中难题的公正答案。免费且可靠。",
                "hero.placeholder": "输入您的问题...",
                "hero.button": "获取答案",
                "hero.examples": "试试这些示例:",
                
                // Stats
                "stats.decisions": "每日决策5000+",
                "stats.satisfaction": "用户满意度93%",
                
                // How It Works
                "how_it_works.title": "YesNoOracle工作原理",
                "how_it_works.subtitle": "我们的决策算法能在几秒钟内提供公正的答案。简单流程如下：",
                "how_it_works.step1_title": "提出问题",
                "how_it_works.step1_desc": "清晰表述您的困境。问题越具体，指导效果越好。",
                "how_it_works.step2_title": "生成答案",
                "how_it_works.step2_desc": "我们的算法使用高级决策框架处理您的查询。",
                "how_it_works.step3_title": "获得明确方向",
                "how_it_works.step3_desc": "立即获得是或否的答案，帮助您自信前行。",
                
                // Features
                "features.title": "强大功能",
                "features.subtitle": "为何数千用户信任YesNoOracle进行日常决策",
                "features.feature1_title": "即时答案",
                "features.feature1_desc": "不再过度思考。几秒钟内获得明确的是/否答案，节省时间和精力。",
                "features.feature2_title": "隐私安全",
                "features.feature2_desc": "您的问题完全保密。我们不存储个人数据或决策历史记录。",
                "features.feature3_title": "决策历史",
                "features.feature3_desc": "高级用户可以保存和查看过去的决策，追踪选择模式。",
                "features.feature4_title": "极速响应",
                "features.feature4_desc": "优化速度。立即获得答案，无需任何不必要的延迟。",
                
                // Testimonials
                "testimonials.title": "数千用户信赖",
                "testimonials.subtitle": "真实用户每天都在做出更好的决策",
                "testimonials.testimonial1": "\"我以前花几个小时做小决定。现在我能立即获得清晰答案。这个工具节省了我大量时间！\"",
                "testimonials.testimonial2": "\"作为管理者，我每天要做几十个决定。YesNoOracle帮助我清除心理障碍，自信前进。\"",
                "testimonials.testimonial3": "\"起初我持怀疑态度，但得到的答案常常与我的直觉一致。对打破决策瘫痪非常有效！\"",
                "testimonials.users": "月活跃用户",
                
                // Pricing
                "pricing.title": "简单透明的价格",
                "pricing.subtitle": "永久免费，可选高级功能",
                "pricing.free_title": "免费方案",
                "pricing.forever": "/永久",
                "pricing.free_feature1": "无限是/否问题",
                "pricing.free_feature2": "即时答案",
                "pricing.free_feature3": "10个预设问题",
                "pricing.free_feature4": "决策历史记录",
                "pricing.free_feature5": "高级分析",
                "pricing.free_button": "免费开始",
                "pricing.popular": "热门",
                "pricing.premium_title": "高级方案",
                "pricing.per_month": "/月",
                "pricing.premium_feature1": "包含免费版所有功能",
                "pricing.premium_feature2": "无限决策历史",
                "pricing.premium_feature3": "详细决策分析",
                "pricing.premium_feature4": "优先支持",
                "pricing.premium_feature5": "无广告体验",
                "pricing.premium_button": "获取高级版",
                
                // FAQ
                "faq.title": "常见问题",
                "faq.subtitle": "关于YesNoOracle的一切",
                "faq.question1": "YesNoOracle真的免费吗？",
                "faq.answer1": "是的！我们的核心决策功能完全永久免费。我们提供带有额外功能的高级方案，但您始终可以免费获得是/否答案。",
                "faq.question2": "决策算法如何工作？",
                "faq.answer2": "我们的专有算法使用先进的随机化技术结合决策科学原理来提供公正答案。该过程旨在消除决策中的人为偏见。",
                "faq.question3": "可以保存决策历史吗？",
                "faq.answer3": "是的，高级用户可以保存和查看所有过去的决策，以追踪选择模式。",
                "faq.question4": "支持多语言吗？",
                "faq.answer4": "是的！我们目前支持英语、西班牙语、中文、日语和韩语。您可以使用顶部导航栏中的下拉菜单切换语言。",
                "faq.question5": "什么类型的问题最有效？",
                "faq.answer5": "YesNoOracle最适合二元决策——可以用简单\"是\"或\"否\"回答的问题。对于多因素的复杂决策，我们建议将其分解为较小的\"是/否\"问题。",
                
                // CTA
                "cta.title": "停止过度思考。开始决策。",
                "cta.subtitle": "加入数千名通过我们简单决策工具找到清晰方向的人。",
                "cta.button": "免费试用YesNoOracle",
                "cta.note": "无需信用卡。永久免费。",
                
                // Footer
                "footer.description": "为犹豫不决者提供即时决策。免费、简单、可靠。",
                "footer.product": "产品",
                "footer.how_it_works": "工作原理",
                "footer.features": "功能特点",
                "footer.pricing": "价格方案",
                "footer.download": "下载",
                "footer.company": "公司",
                "footer.about": "关于我们",
                "footer.careers": "招聘",
                "footer.blog": "博客",
                "footer.press": "新闻",
                "footer.support": "支持",
                "footer.faq": "常见问题",
                "footer.contact": "联系我们",
                "footer.privacy": "隐私政策",
                "footer.terms": "服务条款",
                "footer.rights": "保留所有权利。",
                "footer.created_by": "创建者",
                "footer.contact_label": "联系方式：",
                
                // Result
                "result.your_question": "您的问题：",
                "result.try_again": "再问一个问题"
            },
            ja: {
                // Navigation
                "nav.how_it_works": "使い方",
                "nav.features": "機能",
                "nav.testimonials": "お客様の声",
                "nav.pricing": "料金プラン",
                "nav.faq": "よくある質問",
                "nav.try_free": "無料で試す",
                
                // Hero section
                "hero.title_1": "迷っていますか？今すぐ",
                "hero.title_2": "または",
                "hero.subtitle": "毎日数千人が私たちの意思決定ツールを使用し、人生の難しい質問に迅速で公平な答えを得ています。無料で信頼性が高いです。",
                "hero.placeholder": "質問を入力してください...",
                "hero.button": "答えを取得",
                "hero.examples": "以下の例をお試しください：",
                
                // Stats
                "stats.decisions": "1日5,000件以上の決定",
                "stats.satisfaction": "ユーザー満足度93%",
                
                // How It Works
                "how_it_works.title": "YesNoOracleの仕組み",
                "how_it_works.subtitle": "私たちの意思決定アルゴリズムは、数秒で公平な答えを提供します。シンプルなプロセスは次のとおりです：",
                "how_it_works.step1_title": "質問する",
                "how_it_works.step1_desc": "あなたのジレンマを明確に表現してください。質問が具体的であればあるほど、ガイダンスは良くなります。",
                "how_it_works.step2_title": "答えを生成",
                "how_it_works.step2_desc": "高度な意思決定フレームワークを使用して、お客様のクエリを処理します。",
                "how_it_works.step3_title": "明確な方向性を受け取る",
                "how_it_works.step3_desc": "即座に「はい」または「いいえ」の答えを得て、自信を持って前進できます。",
                
                // Features
                "features.title": "強力な機能",
                "features.subtitle": "なぜ数千人が毎日の決定にYesNoOracleを信頼するのか",
                "features.feature1_title": "即時回答",
                "features.feature1_desc": "過度な思考は不要。数秒で明確な「はい」または「いいえ」の答えを得て、時間と精神エネルギーを節約できます。",
                "features.feature2_title": "プライベート＆セキュア",
                "features.feature2_desc": "ご質問は完全に秘密にされます。個人データや意思決定履歴は保存しません。",
                "features.feature3_title": "意思決定履歴",
                "features.feature3_desc": "プレミアムユーザーは過去の決定を保存して再確認し、選択パターンを追跡できます。",
                "features.feature4_title": "高速応答",
                "features.feature4_desc": "スピードを最適化。不要な遅延なしで即座に回答を得られます。",
                
                // Testimonials
                "testimonials.title": "数千人に信頼されています",
                "testimonials.subtitle": "毎日より良い決定を下している実際の人々",
                "testimonials.testimonial1": "\"小さなことを決めるのに何時間も無駄にしていました。今では即座に明確な答えを得られます。このツールは本当に時間を節約してくれました！\"",
                "testimonials.testimonial2": "\"管理者として、私は毎日数十の決定を下します。YesNoOracleはメンタルブロックをクリアし、自信を持って前進するのに役立っています。\"",
                "testimonials.testimonial3": "\"最初は懐疑的でしたが、得られる答えは私の直感と一致することがよくあります。意思決定の麻痺を解消するのに最適です！\"",
                "testimonials.users": "月間アクティブユーザー",
                
                // Pricing
                "pricing.title": "シンプルで透明な価格設定",
                "pricing.subtitle": "永久無料、オプションのプレミアム機能付き",
                "pricing.free_title": "無料プラン",
                "pricing.forever": "/永久",
                "pricing.free_feature1": "無制限の「はい/いいえ」質問",
                "pricing.free_feature2": "即時回答",
                "pricing.free_feature3": "10のプリセット質問",
                "pricing.free_feature4": "意思決定履歴",
                "pricing.free_feature5": "高度な分析",
                "pricing.free_button": "無料で始める",
                "pricing.popular": "人気",
                "pricing.premium_title": "プレミアムプラン",
                "pricing.per_month": "/月",
                "pricing.premium_feature1": "無料版のすべてを含む",
                "pricing.premium_feature2": "無制限の意思決定履歴",
                "pricing.premium_feature3": "詳細な意思決定分析",
                "pricing.premium_feature4": "優先サポート",
                "pricing.premium_feature5": "広告なしの体験",
                "pricing.premium_button": "プレミアムを取得",
                
                // FAQ
                "faq.title": "よくある質問",
                "faq.subtitle": "YesNoOracleについて知っておくべきすべて",
                "faq.question1": "YesNoOracleは本当に無料ですか？",
                "faq.answer1": "はい！中核となる意思決定機能は完全に無料で永久にご利用いただけます。追加機能を備えたプレミアムプランを提供していますが、「はい/いいえ」の答えは常に無料で取得できます。",
                "faq.question2": "決定アルゴリズムはどのように機能しますか？",
                "faq.answer2": "私たちの独自アルゴリズムは、高度なランダム化技術と意思決定科学の原理を組み合わせて、偏りのない答えを提供します。このプロセスは、意思決定から人間のバイアスを排除するように設計されています。",
                "faq.question3": "決定履歴を保存できますか？",
                "faq.answer3": "はい、プレミアムプランでは過去のすべての決定を保存して再確認でき、時間の経過とともに意思決定パターンを追跡できます。",
                "faq.question4": "複数言語で利用できますか？",
                "faq.answer4": "はい！現在、英語、スペイン語、中国語、日本語、韓国語をサポートしています。上部ナビゲーションのドロップダウンを使用して言語を切り替えることができます。",
                "faq.question5": "どのような質問が最適ですか？",
                "faq.answer5": "YesNoOracleは、単純な「はい」または「いいえ」で答えられる二項決定に最適です。複数の要因が絡む複雑な決定の場合は、小さな「はい/いいえ」の質問に分解することをお勧めします。",
                
                // CTA
                "cta.title": "考えすぎをやめて、決断しましょう。",
                "cta.subtitle": "シンプルな意思決定ツールで明確さを見つけた数千人に参加しましょう。",
                "cta.button": "YesNoOracleを無料で試す",
                "cta.note": "クレジットカード不要。永久無料。",
                
                // Footer
                "footer.description": "迷いやすい人のための即時意思決定。無料、シンプル、信頼性が高い。",
                "footer.product": "製品",
                "footer.how_it_works": "使い方",
                "footer.features": "機能",
                "footer.pricing": "料金プラン",
                "footer.download": "ダウンロード",
                "footer.company": "会社",
                "footer.about": "私たちについて",
                "footer.careers": "採用情報",
                "footer.blog": "ブログ",
                "footer.press": "プレス",
                "footer.support": "サポート",
                "footer.faq": "よくある質問",
                "footer.contact": "お問い合わせ",
                "footer.privacy": "プライバシーポリシー",
                "footer.terms": "利用規約",
                "footer.rights": "全著作権所有。",
                "footer.created_by": "作成者",
                "footer.contact_label": "連絡先：",
                
                // Result
                "result.your_question": "あなたの質問：",
                "result.try_again": "別の質問をする"
            },
            ko: {
                // Navigation
                "nav.how_it_works": "작동 방식",
                "nav.features": "기능",
                "nav.testimonials": "사용자 후기",
                "nav.pricing": "가격 정책",
                "nav.faq": "자주 묻는 질문",
                "nav.try_free": "무료로 사용해보기",
                
                // Hero section
                "hero.title_1": "결정하기 어려우신가요? 즉시",
                "hero.title_2": "또는",
                "hero.subtitle": "매일 수천 명이 우리의 오라클을 사용하여 삶의 어려운 질문에 빠르고 공정한 답변을 얻습니다. 무료이며 신뢰할 수 있습니다.",
                "hero.placeholder": "질문을 입력하세요...",
                "hero.button": "답변 받기",
                "hero.examples": "다음 예시를 시도해 보세요:",
                
                // Stats
                "stats.decisions": "하루 5,000건 이상의 결정",
                "stats.satisfaction": "사용자 만족도 93%",
                
                // How It Works
                "how_it_works.title": "YesNoOracle 작동 방식",
                "how_it_works.subtitle": "우리의 의사 결정 알고리즘은 몇 초 안에 편견 없는 답변을 제공합니다. 간단한 프로세스는 다음과 같습니다:",
                "how_it_works.step1_title": "질문하기",
                "how_it_works.step1_desc": "당신의 딜레마를 명확하게 표현하세요. 질문이 구체적일수록 지도가 더 좋아집니다.",
                "how_it_works.step2_title": "답변 생성",
                "how_it_works.step2_desc": "고급 의사 결정 프레임워크를 사용하여 쿼리를 처리합니다.",
                "how_it_works.step3_title": "명확한 방향 수신",
                "how_it_works.step3_desc": "즉시 예 또는 아니오 답변을 얻고 자신감 있게 나아갈 수 있습니다.",
                
                // Features
                "features.title": "강력한 기능",
                "features.subtitle": "수천 명이 일상 결정에 YesNoOracle을 신뢰하는 이유",
                "features.feature1_title": "즉각적인 답변",
                "features.feature1_desc": "더 이상 과도한 고민은 그만. 몇 초 안에 명확한 예/아니오 답변을 얻어 시간과 정신적 에너지를 절약하세요.",
                "features.feature2_title": "비공개 및 안전",
                "features.feature2_desc": "귀하의 질문은 기밀로 유지됩니다. 개인 데이터나 결정 내역은 저장하지 않습니다.",
                "features.feature3_title": "결정 내역",
                "features.feature3_desc": "프리미엄 사용자는 과거 결정을 저장하고 다시 방문하여 선택 패턴을 추적할 수 있습니다.",
                "features.feature4_title": "초고속",
                "features.feature4_desc": "속도 최적화. 불필요한 지연 없이 즉시 답변을 받으세요.",
                
                // Testimonials
                "testimonials.title": "수천 명이 신뢰합니다",
                "testimonials.subtitle": "매일 더 나은 결정을 내리는 실제 사람들",
                "testimonials.testimonial1": "\"사소한 일을 결정하는 데 몇 시간을 허비했어요. 지금은 즉시 명확한 답을 얻습니다. 이 도구는 정말 많은 시간을 절약해줬어요!\"",
                "testimonials.testimonial2": "\"매니저로서 저는 매일 수십 개의 결정을 내립니다. YesNoOracle은 멘탈 블록을 없애고 자신 있게 나아가는 데 도움이 됩니다.\"",
                "testimonials.testimonial3": "\"처음에는 회의적이었지만, 제가 받는 답변은 종종 제 직감과 일치합니다. 결정 마비를 깨기에 좋습니다!\"",
                "testimonials.users": "월간 활성 사용자",
                
                // Pricing
                "pricing.title": "간단하고 투명한 가격",
                "pricing.subtitle": "영구 무료, 선택적 프리미엄 기능",
                "pricing.free_title": "무료 플랜",
                "pricing.forever": "/영구",
                "pricing.free_feature1": "무제한 예/아니오 질문",
                "pricing.free_feature2": "즉시 답변",
                "pricing.free_feature3": "10가지 사전 설정 질문",
                "pricing.free_feature4": "결정 내역",
                "pricing.free_feature5": "고급 통찰력",
                "pricing.free_button": "무료로 시작",
                "pricing.popular": "인기",
                "pricing.premium_title": "프리미엄 플랜",
                "pricing.per_month": "/월",
                "pricing.premium_feature1": "무료 플랜의 모든 기능 포함",
                "pricing.premium_feature2": "무제한 결정 내역",
                "pricing.premium_feature3": "상세한 결정 통찰력",
                "pricing.premium_feature4": "우선 지원",
                "pricing.premium_feature5": "광고 없는 경험",
                "pricing.premium_button": "프리미엄 받기",
                
                // FAQ
                "faq.title": "자주 묻는 질문",
                "faq.subtitle": "YesNoOracle에 대해 알아야 할 모든 것",
                "faq.question1": "YesNoOracle은 정말 무료인가요?",
                "faq.answer1": "네! 핵심 의사 결정 기능은 영구히 완전 무료로 사용할 수 있습니다. 추가 기능이 포함된 프리미엄 플랜을 제공하지만, 예/아니오 답변은 항상 무료로 얻을 수 있습니다.",
                "faq.question2": "결정 알고리즘은 어떻게 작동하나요?",
                "faq.answer2": "우리의 독점 알고리즘은 고급 무작위화 기술과 의사 결정 과학 원리를 결합하여 편향 없는 답변을 제공합니다. 이 과정은 의사 결정에서 인간의 편향을 제거하도록 설계되었습니다.",
                "faq.question3": "내 결정 내역을 저장할 수 있나요?",
                "faq.answer3": "네, 프리미엄 플랜에서는 과거의 모든 결정을 저장하고 다시 볼 수 있어 시간이 지남에 따라 의사 결정 패턴을 추적할 수 있습니다.",
                "faq.question4": "이 서비스는 여러 언어로 제공되나요?",
                "faq.answer4": "네! 현재 영어, 스페인어, 중국어, 일본어 및 한국어를 지원합니다. 상단 탐색 메뉴의 드롭다운을 사용하여 언어를 전환할 수 있습니다.",
                "faq.question5": "어떤 유형의 질문이 가장 효과적입니까?",
                "faq.answer5": "YesNoOracle은 단순한 \"예\" 또는 \"아니오\"로 답할 수 있는 이분법적 결정에 가장 적합합니다. 여러 요소가 있는 복잡한 결정의 경우 더 작은 예/아니오 질문으로 분해하는 것이 좋습니다.",
                
                // CTA
                "cta.title": "과도한 고민을 멈추고 결정하세요.",
                "cta.subtitle": "우리의 간단한 의사 결정 도구로 명확성을 찾은 수천 명에 합류하세요.",
                "cta.button": "YesNoOracle 무료로 사용해보기",
                "cta.note": "신용카드 불필요. 영구 무료.",
                
                // Footer
                "footer.description": "결정 장애를 가진 분들을 위한 즉각적인 의사 결정. 무료, 간단, 신뢰할 수 있습니다.",
                "footer.product": "제품",
                "footer.how_it_works": "작동 방식",
                "footer.features": "기능",
                "footer.pricing": "가격 정책",
                "footer.download": "다운로드",
                "footer.company": "회사",
                "footer.about": "회사 소개",
                "footer.careers": "채용",
                "footer.blog": "블로그",
                "footer.press": "보도 자료",
                "footer.support": "지원",
                "footer.faq": "자주 묻는 질문",
                "footer.contact": "문의하기",
                "footer.privacy": "개인정보 보호정책",
                "footer.terms": "이용 약관",
                "footer.rights": "모든 권리 보유.",
                "footer.created_by": "제작자",
                "footer.contact_label": "연락처:",
                
                // Result
                "result.your_question": "귀하의 질문:",
                "result.try_again": "다른 질문하기"
            },
            es: {
                // Navigation
                "nav.how_it_works": "Cómo funciona",
                "nav.features": "Características",
                "nav.testimonials": "Testimonios",
                "nav.pricing": "Precios",
                "nav.faq": "Preguntas frecuentes",
                "nav.try_free": "Probar gratis",
                
                // Hero section
                "hero.title_1": "¿No puedes decidir? Obtén un",
                "hero.title_2": "o",
                "hero.subtitle": "Miles usan nuestro oráculo diariamente para obtener respuestas rápidas e imparciales a las difíciles preguntas de la vida. Gratis y confiable.",
                "hero.placeholder": "Haz tu pregunta...",
                "hero.button": "Obtener respuesta",
                "hero.examples": "Prueba estos ejemplos:",
                
                // Stats
                "stats.decisions": "Más de 5,000 decisiones diarias",
                "stats.satisfaction": "93% de satisfacción de usuarios",
                
                // How It Works
                "how_it_works.title": "Cómo funciona YesNoOracle",
                "how_it_works.subtitle": "Nuestro algoritmo de toma de decisiones proporciona respuestas imparciales en segundos. Este es el simple proceso:",
                "how_it_works.step1_title": "Haz tu pregunta",
                "how_it_works.step1_desc": "Formula tu dilema claramente. Cuanto más específica sea tu pregunta, mejor será la orientación.",
                "how_it_works.step2_title": "Genera respuesta",
                "how_it_works.step2_desc": "Nuestro algoritmo procesa tu consulta utilizando marcos avanzados de toma de decisiones.",
                "how_it_works.step3_title": "Recibe dirección clara",
                "how_it_works.step3_desc": "Obtén una respuesta inmediata de sí o no, ayudándote a avanzar con confianza.",
                
                // Features
                "features.title": "Características poderosas",
                "features.subtitle": "Por qué miles confían en YesNoOracle para sus decisiones diarias",
                "features.feature1_title": "Respuestas instantáneas",
                "features.feature1_desc": "No más sobrepensar. Obtén respuestas claras de sí o no en segundos, ahorrando tiempo y energía mental.",
                "features.feature2_title": "Privado y seguro",
                "features.feature2_desc": "Tus preguntas permanecen confidenciales. No almacenamos datos personales ni historial de decisiones.",
                "features.feature3_title": "Historial de decisiones",
                "features.feature3_desc": "Los usuarios premium pueden guardar y revisar decisiones pasadas para rastrear sus patrones de elección.",
                "features.feature4_title": "Súper rápido",
                "features.feature4_desc": "Optimizado para la velocidad. Obtén respuestas al instante sin demoras innecesarias.",
                
                // Testimonials
                "testimonials.title": "Confiado por miles",
                "testimonials.subtitle": "Gente real tomando mejores decisiones cada día",
                "testimonials.testimonial1": "\"Solía perder horas decidiendo cosas pequeñas. Ahora obtengo claridad instantánea. ¡Esta herramienta me ha ahorrado tanto tiempo!\"",
                "testimonials.testimonial2": "\"Como gerente, tomo docenas de decisiones diarias. YesNoOracle me ayuda a despejar bloqueos mentales y avanzar con confianza.\"",
                "testimonials.testimonial3": "\"Al principio era escéptica, pero las respuestas que obtengo a menudo se alinean con mi intuición. ¡Genial para romper la parálisis de decisión!\"",
                "testimonials.users": "Usuarios activos mensuales",
                
                // Pricing
                "pricing.title": "Precios simples y transparentes",
                "pricing.subtitle": "Gratis para siempre, con funciones premium opcionales",
                "pricing.free_title": "Plan gratuito",
                "pricing.forever": "/para siempre",
                "pricing.free_feature1": "Preguntas sí/no ilimitadas",
                "pricing.free_feature2": "Respuestas instantáneas",
                "pricing.free_feature3": "10 preguntas preestablecidas",
                "pricing.free_feature4": "Historial de decisiones",
                "pricing.free_feature5": "Análisis avanzados",
                "pricing.free_button": "Comenzar gratis",
                "pricing.popular": "POPULAR",
                "pricing.premium_title": "Plan premium",
                "pricing.per_month": "/mes",
                "pricing.premium_feature1": "Todo en Gratis",
                "pricing.premium_feature2": "Historial de decisiones ilimitado",
                "pricing.premium_feature3": "Análisis detallados de decisiones",
                "pricing.premium_feature4": "Soporte prioritario",
                "pricing.premium_feature5": "Experiencia sin anuncios",
                "pricing.premium_button": "Obtener Premium",
                
                // FAQ
                "faq.title": "Preguntas frecuentes",
                "faq.subtitle": "Todo lo que necesitas saber sobre YesNoOracle",
                "faq.question1": "¿Es YesNoOracle realmente gratuito?",
                "faq.answer1": "¡Sí! Nuestra funcionalidad central de toma de decisiones es completamente gratuita para siempre. Ofrecemos un plan premium con funciones adicionales, pero siempre puedes obtener respuestas sí/no sin pagar.",
                "faq.question2": "¿Cómo funciona el algoritmo de decisión?",
                "faq.answer2": "Nuestro algoritmo patentado utiliza técnicas avanzadas de aleatorización combinadas con principios de ciencia de decisiones para proporcionar respuestas imparciales. El proceso está diseñado para eliminar el sesgo humano de tu toma de decisiones.",
                "faq.question3": "¿Puedo guardar mi historial de decisiones?",
                "faq.answer3": "Sí, con nuestro plan Premium puedes guardar y revisar todas tus decisiones pasadas para rastrear tus patrones de elección a lo largo del tiempo.",
                "faq.question4": "¿Este servicio está disponible en varios idiomas?",
                "faq.answer4": "¡Sí! Actualmente admitimos inglés, español, chino, japonés y coreano. Puedes cambiar de idioma usando el menú desplegable en la navegación superior.",
                "faq.question5": "¿Qué tipos de preguntas funcionan mejor?",
                "faq.answer5": "YesNoOracle funciona mejor para decisiones binarias: preguntas que pueden responderse con un simple \"sí\" o \"no\". Para decisiones complejas con múltiples factores, recomendamos desglosarlas en preguntas más pequeñas de sí/no.",
                
                // CTA
                "cta.title": "Deja de pensar demasiado. Empieza a decidir.",
                "cta.subtitle": "Únete a miles que han encontrado claridad con nuestra simple herramienta de toma de decisiones.",
                "cta.button": "Probar YesNoOracle Gratis",
                "cta.note": "Sin tarjeta de crédito. Gratis para siempre.",
                
                // Footer
                "footer.description": "Toma de decisiones instantánea para los indecisos. Gratis, simple, confiable.",
                "footer.product": "Producto",
                "footer.how_it_works": "Cómo funciona",
                "footer.features": "Características",
                "footer.pricing": "Precios",
                "footer.download": "Descargar",
                "footer.company": "Empresa",
                "footer.about": "Sobre nosotros",
                "footer.careers": "Carreras",
                "footer.blog": "Blog",
                "footer.press": "Prensa",
                "footer.support": "Soporte",
                "footer.faq": "Preguntas frecuentes",
                "footer.contact": "Contacto",
                "footer.privacy": "Política de privacidad",
                "footer.terms": "Términos de servicio",
                "footer.rights": "Todos los derechos reservados.",
                "footer.created_by": "Creado por",
                "footer.contact_label": "Contacto:",
                
                // Result
                "result.your_question": "Tu pregunta:",
                "result.try_again": "Hacer otra pregunta"
            }
        };

        // Language-specific preset questions
        const presetQuestions = {
            en: [
                "Should I take this job offer?",
                "Is today a good day to invest?",
                "Should I ask for a raise?",
                "Will this idea succeed?",
                "Should I move to a new city?",
                "Is it time to start my own business?",
                "Should I learn a new skill?",
                "Will this relationship work?",
                "Should I buy this product?",
                "Is now the right time to travel?"
            ],
            zh: [
                "我应该接受这个工作机会吗？",
                "今天是投资的好日子吗？",
                "我应该要求加薪吗？",
                "这个想法会成功吗？",
                "我应该搬到新城市吗？",
                "现在是创业的时候吗？",
                "我应该学习新技能吗？",
                "这段关系会成功吗？",
                "我应该购买这个产品吗？",
                "现在是旅行的好时机吗？"
            ],
            ja: [
                "この仕事のオファーを受けるべきですか？",
                "今日は投資に良い日ですか？",
                "昇給を要求すべきですか？",
                "このアイデアは成功しますか？",
                "新しい都市に引っ越すべきですか？",
                "起業する時が来ましたか？",
                "新しいスキルを学ぶべきですか？",
                "この関係はうまくいきますか？",
                "この製品を買うべきですか？",
                "今は旅行に適した時期ですか？"
            ],
            ko: [
                "이 일자리 제안을 받아들여야 할까요?",
                "오늘은 투자하기 좋은 날인가요?",
                "급여 인상을 요청해야 할까요?",
                "이 아이디어는 성공할까요?",
                "새 도시로 이사해야 할까요?",
                "지금이 사업을 시작할 때인가요?",
                "새로운 기술을 배워야 할까요?",
                "이 관계는 잘 될까요?",
                "이 제품을 구매해야 할까요?",
                "지금이 여행하기 좋은 시기인가요?"
            ],
            es: [
                "¿Debería aceptar esta oferta de trabajo?",
                "¿Es hoy un buen día para invertir?",
                "¿Debería pedir un aumento?",
                "¿Esta idea tendrá éxito?",
                "¿Debería mudarme a una nueva ciudad?",
                "¿Es hora de comenzar mi propio negocio?",
                "¿Debería aprender una nueva habilidad?",
                "¿Funcionará esta relación?",
                "¿Debería comprar este producto?",
                "¿Es ahora el momento adecuado para viajar?"
            ]
        };

        // Initialize with English
        let currentLang = 'en';
        
        // Function to set the language
        function setLanguage(lang) {
            currentLang = lang;
            
            // Update language selector
            document.getElementById('language-selector').value = lang;
            
            // Update all elements with data-i18n-key
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18n[lang] && i18n[lang][key]) {
                    element.textContent = i18n[lang][key];
                }
            });
            
            // Update placeholders
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (i18n[lang] && i18n[lang][key]) {
                    element.placeholder = i18n[lang][key];
                }
            });
            
            // Update preset questions
            updatePresetQuestions(lang);
        }
        
        // Update preset questions based on language
        function updatePresetQuestions(lang) {
            const container = document.getElementById('preset-questions-container');
            container.innerHTML = '';
            
            if (presetQuestions[lang]) {
                presetQuestions[lang].forEach(question => {
                    const button = document.createElement('button');
                    button.className = 'preset-question text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full';
                    button.textContent = question;
                    button.addEventListener('click', () => {
                        document.getElementById('question-input').value = question;
                    });
                    container.appendChild(button);
                });
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Set initial language
            setLanguage(currentLang);
            
            // Language selector change event
            document.getElementById('language-selector').addEventListener('change', (e) => {
                setLanguage(e.target.value);
            });
            
            const generateBtn = document.getElementById('generate-btn');
            const questionInput = document.getElementById('question-input');
            const resultContainer = document.getElementById('result-container');
            const resultElement = document.getElementById('result');
            const questionDisplay = document.getElementById('question-display');
            const tryAgainBtn = document.getElementById('try-again');
            
            // Generate answer
            generateBtn.addEventListener('click', function() {
                const question = questionInput.value.trim();
                if (question === '') {
                    alert(i18n[currentLang]['hero.placeholder'] || 'Please enter a question');
                    return;
                }
                
                // Display the question
                questionDisplay.textContent = question;
                
                // Generate random answer (Yes or No)
                const answers = ['YES', 'NO'];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                
                // Set color based on answer
                if (randomAnswer === 'YES') {
                    resultElement.className = 'text-green-500 text-6xl font-bold result-animation';
                } else {
                    resultElement.className = 'text-red-500 text-6xl font-bold result-animation';
                }
                
                resultElement.textContent = randomAnswer;
                
                // Show result container
                resultContainer.classList.remove('hidden');
                
                // Scroll to results
                resultContainer.scrollIntoView({ behavior: 'smooth' });
            });
            
            // Try again button
            tryAgainBtn.addEventListener('click', function() {
                resultContainer.classList.add('hidden');
                questionInput.value = '';
                questionInput.focus();
            });
        });
    </script>
</body>
</html>