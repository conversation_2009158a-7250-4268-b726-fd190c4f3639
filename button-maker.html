<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free online Button Maker tool - Create custom buttons in seconds">
    <meta name="keywords" content="button maker, custom buttons, online tool, web design, UI elements">
    <link rel="canonical" href="https://yesnooracle.xyz/button-maker.html">
    <title>Button Maker - Create Custom Buttons Online | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .button-preview {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .button-preview:hover {
            transform: scale(1.05);
        }
        .color-picker-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }
        .color-picker-wrapper:hover {
            border-color: #0071e3;
        }
        .size-preview {
            display: inline-block;
            margin: 4px;
            padding: 8px 16px;
            border-radius: 6px;
            background: #f3f4f6;
            transition: all 0.2s ease;
        }
        .size-preview.small { padding: 6px 12px; font-size: 14px; }
        .size-preview.medium { padding: 8px 16px; font-size: 16px; }
        .size-preview.large { padding: 12px 24px; font-size: 18px; }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 apple-gradient">
            <div class="container mx-auto px-4 text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    <span data-i18n="button_maker.title">Create Custom Buttons in Seconds</span>
                </h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-12" data-i18n="button_maker.subtitle">
                    Design professional buttons without any coding knowledge. Perfect for websites, apps, and digital projects.
                </p>

                <!-- Button Maker Interface -->
                <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Configuration Panel -->
                    <div class="bg-white rounded-xl shadow-lg p-8">
                        <h2 class="text-2xl font-semibold mb-6" data-i18n="button_maker.customize">Customize Your Button</h2>

                        <div class="space-y-6">
                            <!-- Button Text -->
                            <div>
                                <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.button_text">Button Text</label>
                                <input id="button-text" type="text" class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    placeholder="Click me" data-i18n-placeholder="button_maker.text_placeholder" value="Click me">
                            </div>

                            <!-- Button Color -->
                            <div>
                                <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.button_color">Button Color</label>
                                <div class="color-picker-wrapper">
                                    <input id="button-color" type="color" class="w-full h-12 cursor-pointer border-0" value="#0071e3">
                                </div>
                                <div class="mt-2 flex flex-wrap gap-2">
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #0071e3" onclick="setColor('#0071e3')"></button>
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #10b981" onclick="setColor('#10b981')"></button>
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #f59e0b" onclick="setColor('#f59e0b')"></button>
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #ef4444" onclick="setColor('#ef4444')"></button>
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #8b5cf6" onclick="setColor('#8b5cf6')"></button>
                                    <button class="w-8 h-8 rounded border-2 border-gray-300" style="background: #1f2937" onclick="setColor('#1f2937')"></button>
                                </div>
                            </div>

                            <!-- Button Size -->
                            <div>
                                <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.button_size">Button Size</label>
                                <select id="button-size" class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="small" data-i18n="button_maker.size_small">Small</option>
                                    <option value="medium" selected data-i18n="button_maker.size_medium">Medium</option>
                                    <option value="large" data-i18n="button_maker.size_large">Large</option>
                                </select>
                                <div class="mt-2 text-sm text-gray-500">
                                    <span class="size-preview small">Small</span>
                                    <span class="size-preview medium">Medium</span>
                                    <span class="size-preview large">Large</span>
                                </div>
                            </div>

                            <!-- Button Style -->
                            <div>
                                <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.button_style">Button Style</label>
                                <select id="button-style" class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="solid" data-i18n="button_maker.style_solid">Solid</option>
                                    <option value="outline" data-i18n="button_maker.style_outline">Outline</option>
                                    <option value="ghost" data-i18n="button_maker.style_ghost">Ghost</option>
                                </select>
                            </div>

                            <!-- Border Radius -->
                            <div>
                                <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.border_radius">Border Radius</label>
                                <input id="border-radius" type="range" min="0" max="25" value="8" class="w-full">
                                <div class="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>0px</span>
                                    <span id="radius-value">8px</span>
                                    <span>25px</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Panel -->
                    <div class="bg-white rounded-xl shadow-lg p-8 flex flex-col">
                        <h2 class="text-2xl font-semibold mb-6" data-i18n="button_maker.preview">Button Preview</h2>

                        <div class="flex-grow flex items-center justify-center bg-gray-50 rounded-lg p-8 mb-6">
                            <button id="preview-button" class="button-preview px-6 py-3 rounded-lg bg-primary text-white text-lg font-medium">
                                Click me
                            </button>
                        </div>

                        <!-- CSS Code Output -->
                        <div class="mb-6">
                            <label class="block text-gray-700 font-medium mb-2" data-i18n="button_maker.css_code">CSS Code</label>
                            <textarea id="css-output" class="w-full h-32 p-3 bg-gray-100 border rounded-lg text-sm font-mono" readonly></textarea>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-3">
                            <button id="copy-css" class="w-full bg-primary text-white py-3 rounded-lg hover:bg-blue-600 transition-colors" data-i18n="button_maker.copy_css">
                                Copy CSS Code
                            </button>
                            <button id="download-button" class="w-full bg-accent text-white py-3 rounded-lg hover:bg-orange-700 transition-colors" data-i18n="button_maker.download">
                                Download as HTML
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="button_maker.features_title">Why Use Our Button Maker?</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="button_maker.features_subtitle">
                    Create professional buttons with our easy-to-use online tool. No design experience required.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature1_title">No Coding Required</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature1_desc">Create professional buttons without writing a single line of code. Perfect for designers and developers alike.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature2_title">Instant Results</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature2_desc">See your button changes in real-time as you customize it. What you see is what you get.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature3_title">Free Forever</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature3_desc">Our button maker is completely free with no hidden costs. Create unlimited buttons for all your projects.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature4_title">Export Ready Code</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature4_desc">Get clean, optimized CSS code that you can copy and paste directly into your projects.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature5_title">Multiple Styles</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature5_desc">Choose from solid, outline, and ghost button styles to match your design needs.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature6_title">Mobile Responsive</h3>
                        <p class="text-gray-600" data-i18n="button_maker.feature6_desc">All generated buttons are mobile-responsive and work perfectly on any device size.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Button maker functionality
            const buttonText = document.getElementById('button-text');
            const buttonColor = document.getElementById('button-color');
            const buttonSize = document.getElementById('button-size');
            const buttonStyle = document.getElementById('button-style');
            const borderRadius = document.getElementById('border-radius');
            const radiusValue = document.getElementById('radius-value');
            const previewButton = document.getElementById('preview-button');
            const cssOutput = document.getElementById('css-output');
            const copyCssBtn = document.getElementById('copy-css');
            const downloadBtn = document.getElementById('download-button');

            // Size configurations
            const sizeConfig = {
                small: { padding: '8px 16px', fontSize: '14px' },
                medium: { padding: '12px 24px', fontSize: '16px' },
                large: { padding: '16px 32px', fontSize: '18px' }
            };

            // Update preview and CSS
            function updateButton() {
                const text = buttonText.value || 'Click me';
                const color = buttonColor.value;
                const size = buttonSize.value;
                const style = buttonStyle.value;
                const radius = borderRadius.value;

                // Update preview button
                previewButton.textContent = text;
                previewButton.style.borderRadius = radius + 'px';
                previewButton.style.padding = sizeConfig[size].padding;
                previewButton.style.fontSize = sizeConfig[size].fontSize;

                // Apply style
                if (style === 'solid') {
                    previewButton.style.backgroundColor = color;
                    previewButton.style.color = 'white';
                    previewButton.style.border = 'none';
                } else if (style === 'outline') {
                    previewButton.style.backgroundColor = 'transparent';
                    previewButton.style.color = color;
                    previewButton.style.border = `2px solid ${color}`;
                } else if (style === 'ghost') {
                    previewButton.style.backgroundColor = 'transparent';
                    previewButton.style.color = color;
                    previewButton.style.border = 'none';
                }

                // Update radius display
                radiusValue.textContent = radius + 'px';

                // Generate CSS
                generateCSS();
            }

            // Generate CSS code
            function generateCSS() {
                const color = buttonColor.value;
                const size = buttonSize.value;
                const style = buttonStyle.value;
                const radius = borderRadius.value;

                let css = `.custom-button {\n`;
                css += `  padding: ${sizeConfig[size].padding};\n`;
                css += `  font-size: ${sizeConfig[size].fontSize};\n`;
                css += `  border-radius: ${radius}px;\n`;
                css += `  font-weight: 500;\n`;
                css += `  cursor: pointer;\n`;
                css += `  transition: all 0.2s ease;\n`;

                if (style === 'solid') {
                    css += `  background-color: ${color};\n`;
                    css += `  color: white;\n`;
                    css += `  border: none;\n`;
                } else if (style === 'outline') {
                    css += `  background-color: transparent;\n`;
                    css += `  color: ${color};\n`;
                    css += `  border: 2px solid ${color};\n`;
                } else if (style === 'ghost') {
                    css += `  background-color: transparent;\n`;
                    css += `  color: ${color};\n`;
                    css += `  border: none;\n`;
                }

                css += `}\n\n`;
                css += `.custom-button:hover {\n`;
                if (style === 'solid') {
                    css += `  opacity: 0.9;\n`;
                    css += `  transform: translateY(-1px);\n`;
                } else {
                    css += `  background-color: ${color};\n`;
                    css += `  color: white;\n`;
                }
                css += `}`;

                cssOutput.value = css;
            }

            // Set color from preset
            window.setColor = function(color) {
                buttonColor.value = color;
                updateButton();
            };

            // Event listeners
            buttonText.addEventListener('input', updateButton);
            buttonColor.addEventListener('input', updateButton);
            buttonSize.addEventListener('change', updateButton);
            buttonStyle.addEventListener('change', updateButton);
            borderRadius.addEventListener('input', updateButton);

            // Copy CSS functionality
            copyCssBtn.addEventListener('click', function() {
                cssOutput.select();
                document.execCommand('copy');

                const originalText = showLoading(copyCssBtn);
                setTimeout(() => {
                    hideLoading(copyCssBtn, 'Copied!');
                    setTimeout(() => {
                        copyCssBtn.textContent = originalText;
                    }, 1000);
                }, 500);
            });

            // Download HTML functionality
            downloadBtn.addEventListener('click', function() {
                const text = buttonText.value || 'Click me';
                const css = cssOutput.value;

                const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Button</title>
    <style>
${css}
    </style>
</head>
<body>
    <button class="custom-button">${text}</button>
</body>
</html>`;

                const blob = new Blob([html], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'custom-button.html';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });

            // Initialize
            updateButton();
        });
    </script>
</body>
</html>