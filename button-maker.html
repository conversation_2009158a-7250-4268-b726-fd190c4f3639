<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free online Button Maker tool - Create custom buttons in seconds">
    <title>Button Maker - Create Custom Buttons Online | YesNoOracle</title>
    
    <!-- 复用index.html的CSS和JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 页面特有样式 -->
    <style>
        /* 按钮制作器特有样式 */
        .button-preview {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-white">
    <!-- 复用header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... 复用index.html的header代码 ... -->
    </header>

    <!-- 页面特有内容 -->
    <section class="py-20 apple-gradient">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <span data-i18n="button_maker.title">Create Custom Buttons in Seconds</span>
            </h1>
            
            <!-- 按钮制作器交互区域 -->
            <div class="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- 按钮配置表单 -->
                <div class="bg-white rounded-xl shadow-lg p-8">
                    <h2 class="text-2xl font-semibold mb-6" data-i18n="button_maker.customize">Customize Your Button</h2>
                    
                    <!-- 表单控件 -->
                    <div class="space-y-4">
                        <!-- 文本输入 -->
                        <div>
                            <label class="block text-gray-700 mb-2" data-i18n="button_maker.button_text">Button Text</label>
                            <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Click me">
                        </div>
                        
                        <!-- 颜色选择 -->
                        <div>
                            <label class="block text-gray-700 mb-2" data-i18n="button_maker.button_color">Button Color</label>
                            <input type="color" class="w-full h-10 cursor-pointer" value="#0071e3">
                        </div>
                        
                        <!-- 尺寸选择 -->
                        <div>
                            <label class="block text-gray-700 mb-2" data-i18n="button_maker.button_size">Button Size</label>
                            <select class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="small">Small</option>
                                <option value="medium" selected>Medium</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                        
                        <!-- 生成按钮 -->
                        <button class="w-full bg-primary text-white py-3 rounded-lg hover:bg-blue-600 transition-colors mt-6" data-i18n="button_maker.generate">Generate Button</button>
                    </div>
                </div>
                
                <!-- 按钮预览 -->
                <div class="bg-white rounded-xl shadow-lg p-8 flex flex-col">
                    <h2 class="text-2xl font-semibold mb-6" data-i18n="button_maker.preview">Button Preview</h2>
                    
                    <div class="flex-grow flex items-center justify-center">
                        <button class="button-preview px-6 py-3 rounded-lg bg-primary text-white text-lg">
                            Click me
                        </button>
                    </div>
                    
                    <!-- 下载按钮 -->
                    <div class="mt-8">
                        <button class="w-full bg-accent text-white py-3 rounded-lg hover:bg-orange-700 transition-colors" data-i18n="button_maker.download">Download Button</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 其他内容部分 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="button_maker.features_title">Why Use Our Button Maker?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 特性1 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature1_title">No Coding Required</h3>
                    <p class="text-gray-600" data-i18n="button_maker.feature1_desc">Create professional buttons without writing a single line of code.</p>
                </div>
                
                <!-- 特性2 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature2_title">Instant Results</h3>
                    <p class="text-gray-600" data-i18n="button_maker.feature2_desc">See your button changes in real-time as you customize it.</p>
                </div>
                
                <!-- 特性3 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="button_maker.feature3_title">Free Forever</h3>
                    <p class="text-gray-600" data-i18n="button_maker.feature3_desc">Our button maker is completely free with no hidden costs.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 复用footer -->
    <footer class="bg-gray-100 py-12">
        <!-- ... 复用index.html的footer代码 ... -->
    </footer>

    <!-- 页面特有JS -->
    <script>
        // 按钮制作器交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 这里添加按钮制作器的交互代码
        });
    </script>
</body>
</html>