<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Button Clicker - Interactive clicking game and tool to count your clicks">
    <meta name="keywords" content="button clicker, clicking game, interactive button, fun tool">
    <link rel="canonical" href="https://yesnooracle.xyz/button-clicker.html">
    <title>Button Clicker - Interactive Clicking Game | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .clicker-container {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
            position: relative;
            overflow: hidden;
        }
        .clicker-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="click-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="2" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23click-pattern)"/></svg>') repeat;
        }
        .click-button {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        .click-button:active {
            transform: scale(0.95);
        }
        .click-button.clicked {
            animation: clickPulse 0.3s ease-out;
        }
        @keyframes clickPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .count-display {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .achievement {
            animation: achievementPop 0.5s ease-out;
        }
        @keyframes achievementPop {
            0% { transform: scale(0) rotate(-180deg); opacity: 0; }
            50% { transform: scale(1.2) rotate(-90deg); opacity: 1; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        .stats-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .click-effect {
            position: absolute;
            pointer-events: none;
            color: #fbbf24;
            font-weight: bold;
            animation: clickEffect 1s ease-out forwards;
        }
        @keyframes clickEffect {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-50px) scale(1.5); opacity: 0; }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 clicker-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="buttonclicker.title">Button Clicker Game</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="buttonclicker.subtitle">
                    Click your way to satisfaction! Test your clicking speed and unlock achievements.
                </p>

                <!-- Game Interface -->
                <div class="max-w-4xl mx-auto stats-card rounded-xl p-8 mb-8">
                    <!-- Stats Display -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="text-center">
                            <div class="text-4xl md:text-6xl font-bold count-display mb-2" id="click-count">0</div>
                            <p class="text-gray-600" data-i18n="buttonclicker.total_clicks">Total Clicks</p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl md:text-3xl font-bold text-blue-600 mb-2" id="clicks-per-second">0</div>
                            <p class="text-gray-600" data-i18n="buttonclicker.clicks_per_second">Clicks/Second</p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl md:text-3xl font-bold text-green-600 mb-2" id="best-streak">0</div>
                            <p class="text-gray-600" data-i18n="buttonclicker.best_streak">Best Streak</p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl md:text-3xl font-bold text-purple-600 mb-2" id="achievements-count">0</div>
                            <p class="text-gray-600" data-i18n="buttonclicker.achievements">Achievements</p>
                        </div>
                    </div>

                    <!-- Main Click Button -->
                    <div class="relative mb-8">
                        <button id="main-click-button" class="click-button bg-gradient-to-r from-blue-500 to-blue-700 text-white px-16 py-8 rounded-full text-3xl font-bold hover:from-blue-600 hover:to-blue-800 transition-all duration-200 shadow-2xl" data-i18n="buttonclicker.click">
                            CLICK ME!
                        </button>
                    </div>

                    <!-- Control Buttons -->
                    <div class="flex justify-center space-x-4 mb-6">
                        <button id="reset-button" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="buttonclicker.reset">
                            Reset
                        </button>
                        <button id="auto-clicker" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors" data-i18n="buttonclicker.auto_click">
                            Auto Click
                        </button>
                        <button id="challenge-mode" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors" data-i18n="buttonclicker.challenge">
                            Challenge Mode
                        </button>
                    </div>

                    <!-- Achievements Display -->
                    <div id="achievements-container" class="hidden">
                        <h3 class="text-xl font-semibold mb-4" data-i18n="buttonclicker.recent_achievement">🎉 Achievement Unlocked!</h3>
                        <div id="achievement-text" class="text-lg text-green-600 font-medium"></div>
                    </div>
                </div>

                <!-- Game Mode Info -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buttonclicker.feature1">Unlimited Clicks</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buttonclicker.feature2">Achievement System</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buttonclicker.feature3">Speed Tracking</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="buttonclicker.content_title">About The Button Clicker Game</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="buttonclicker.content_subtitle">
                    More than just clicking - it's a game of speed, endurance, and achievement.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-blue-100">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buttonclicker.feature1_title">Speed Testing</h3>
                        <p class="text-gray-600" data-i18n="buttonclicker.feature1_desc">Test your clicking speed and see how many clicks per second you can achieve. Perfect for competitive clicking!</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-green-100">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buttonclicker.feature2_title">Achievement System</h3>
                        <p class="text-gray-600" data-i18n="buttonclicker.feature2_desc">Unlock achievements as you reach clicking milestones. From first click to clicking master!</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buttonclicker.feature3_title">Challenge Modes</h3>
                        <p class="text-gray-600" data-i18n="buttonclicker.feature3_desc">Try different game modes including auto-clicker and timed challenges for extra fun.</p>
                    </div>
                </div>

                <!-- Game Tips -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="buttonclicker.tips_title">Pro Clicking Tips</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="buttonclicker.tips_speed">Speed Techniques</h4>
                            <ul class="space-y-2 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip1">Use multiple fingers for faster clicking</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip2">Find your optimal clicking rhythm</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip3">Take breaks to avoid fatigue</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="buttonclicker.tips_achievements">Achievement Hunting</h4>
                            <ul class="space-y-2 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip4">Aim for consistent clicking streaks</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip5">Try different game modes</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">•</span>
                                    <span data-i18n="buttonclicker.tip6">Challenge yourself with speed goals</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let clickCount = 0;
            let clicksPerSecond = 0;
            let bestStreak = 0;
            let currentStreak = 0;
            let achievementsUnlocked = 0;
            let autoClickerActive = false;
            let challengeModeActive = false;
            let lastClickTime = 0;
            let clickTimes = [];
            let autoClickerInterval;

            // DOM elements
            const clickButton = document.getElementById('main-click-button');
            const resetButton = document.getElementById('reset-button');
            const autoClickerButton = document.getElementById('auto-clicker');
            const challengeModeButton = document.getElementById('challenge-mode');
            const clickCountDisplay = document.getElementById('click-count');
            const clicksPerSecondDisplay = document.getElementById('clicks-per-second');
            const bestStreakDisplay = document.getElementById('best-streak');
            const achievementsCountDisplay = document.getElementById('achievements-count');
            const achievementsContainer = document.getElementById('achievements-container');
            const achievementText = document.getElementById('achievement-text');

            // Achievements
            const achievements = [
                { id: 'first_click', name: 'First Click!', description: 'Made your first click', threshold: 1 },
                { id: 'getting_started', name: 'Getting Started', description: 'Reached 10 clicks', threshold: 10 },
                { id: 'clicking_along', name: 'Clicking Along', description: 'Reached 50 clicks', threshold: 50 },
                { id: 'century_club', name: 'Century Club', description: 'Reached 100 clicks', threshold: 100 },
                { id: 'dedicated_clicker', name: 'Dedicated Clicker', description: 'Reached 500 clicks', threshold: 500 },
                { id: 'click_master', name: 'Click Master', description: 'Reached 1000 clicks', threshold: 1000 },
                { id: 'speed_demon', name: 'Speed Demon', description: 'Achieved 10 clicks per second', threshold: 10, type: 'speed' },
                { id: 'lightning_fast', name: 'Lightning Fast', description: 'Achieved 15 clicks per second', threshold: 15, type: 'speed' }
            ];

            let unlockedAchievements = new Set();

            // Click effect animation
            function createClickEffect(x, y) {
                const effect = document.createElement('div');
                effect.className = 'click-effect';
                effect.textContent = '+1';
                effect.style.left = x + 'px';
                effect.style.top = y + 'px';
                effect.style.position = 'fixed';
                effect.style.zIndex = '1000';
                document.body.appendChild(effect);

                setTimeout(() => {
                    document.body.removeChild(effect);
                }, 1000);
            }

            // Update displays
            function updateDisplays() {
                clickCountDisplay.textContent = clickCount.toLocaleString();
                clicksPerSecondDisplay.textContent = clicksPerSecond.toFixed(1);
                bestStreakDisplay.textContent = bestStreak;
                achievementsCountDisplay.textContent = achievementsUnlocked;
            }

            // Calculate clicks per second
            function calculateClicksPerSecond() {
                const now = Date.now();
                clickTimes = clickTimes.filter(time => now - time < 1000);
                clicksPerSecond = clickTimes.length;
            }

            // Check achievements
            function checkAchievements() {
                achievements.forEach(achievement => {
                    if (!unlockedAchievements.has(achievement.id)) {
                        let shouldUnlock = false;

                        if (achievement.type === 'speed') {
                            shouldUnlock = clicksPerSecond >= achievement.threshold;
                        } else {
                            shouldUnlock = clickCount >= achievement.threshold;
                        }

                        if (shouldUnlock) {
                            unlockAchievement(achievement);
                        }
                    }
                });
            }

            // Unlock achievement
            function unlockAchievement(achievement) {
                unlockedAchievements.add(achievement.id);
                achievementsUnlocked++;

                achievementText.textContent = `${achievement.name}: ${achievement.description}`;
                achievementsContainer.classList.remove('hidden');
                achievementsContainer.classList.add('achievement');

                setTimeout(() => {
                    achievementsContainer.classList.add('hidden');
                    achievementsContainer.classList.remove('achievement');
                }, 3000);
            }

            // Handle click
            function handleClick(event) {
                clickCount++;
                currentStreak++;

                const now = Date.now();
                clickTimes.push(now);

                // Update best streak
                if (currentStreak > bestStreak) {
                    bestStreak = currentStreak;
                }

                // Calculate clicks per second
                calculateClicksPerSecond();

                // Add click animation
                clickButton.classList.add('clicked');
                setTimeout(() => {
                    clickButton.classList.remove('clicked');
                }, 300);

                // Create click effect
                if (event) {
                    const rect = clickButton.getBoundingClientRect();
                    const x = event.clientX || rect.left + rect.width / 2;
                    const y = event.clientY || rect.top + rect.height / 2;
                    createClickEffect(x, y);
                }

                // Update displays
                updateDisplays();

                // Check achievements
                checkAchievements();

                lastClickTime = now;
            }

            // Reset game
            function resetGame() {
                clickCount = 0;
                clicksPerSecond = 0;
                bestStreak = 0;
                currentStreak = 0;
                achievementsUnlocked = 0;
                clickTimes = [];
                unlockedAchievements.clear();

                if (autoClickerInterval) {
                    clearInterval(autoClickerInterval);
                    autoClickerActive = false;
                    autoClickerButton.textContent = 'Auto Click';
                    autoClickerButton.classList.remove('bg-red-500');
                    autoClickerButton.classList.add('bg-green-500');
                }

                updateDisplays();
            }

            // Toggle auto clicker
            function toggleAutoClicker() {
                if (autoClickerActive) {
                    clearInterval(autoClickerInterval);
                    autoClickerActive = false;
                    autoClickerButton.textContent = 'Auto Click';
                    autoClickerButton.classList.remove('bg-red-500');
                    autoClickerButton.classList.add('bg-green-500');
                } else {
                    autoClickerActive = true;
                    autoClickerButton.textContent = 'Stop Auto';
                    autoClickerButton.classList.remove('bg-green-500');
                    autoClickerButton.classList.add('bg-red-500');

                    autoClickerInterval = setInterval(() => {
                        handleClick();
                    }, 100); // 10 clicks per second
                }
            }

            // Toggle challenge mode
            function toggleChallengeMode() {
                challengeModeActive = !challengeModeActive;

                if (challengeModeActive) {
                    challengeModeButton.textContent = 'Exit Challenge';
                    challengeModeButton.classList.remove('bg-purple-500');
                    challengeModeButton.classList.add('bg-red-500');

                    // Start 30-second challenge
                    let timeLeft = 30;
                    const challengeInterval = setInterval(() => {
                        timeLeft--;
                        challengeModeButton.textContent = `Challenge: ${timeLeft}s`;

                        if (timeLeft <= 0) {
                            clearInterval(challengeInterval);
                            challengeModeActive = false;
                            challengeModeButton.textContent = 'Challenge Mode';
                            challengeModeButton.classList.remove('bg-red-500');
                            challengeModeButton.classList.add('bg-purple-500');

                            alert(`Challenge Complete! You clicked ${clickCount} times in 30 seconds!`);
                        }
                    }, 1000);
                } else {
                    challengeModeButton.textContent = 'Challenge Mode';
                    challengeModeButton.classList.remove('bg-red-500');
                    challengeModeButton.classList.add('bg-purple-500');
                }
            }

            // Event listeners
            clickButton.addEventListener('click', handleClick);
            resetButton.addEventListener('click', resetGame);
            autoClickerButton.addEventListener('click', toggleAutoClicker);
            challengeModeButton.addEventListener('click', toggleChallengeMode);

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.code === 'Space') {
                    e.preventDefault();
                    handleClick();
                }
            });

            // Initialize displays
            updateDisplays();
        });
    </script>
</body>
</html>