<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Button Clicker - A simple tool to count your clicks">
    <title>Button Clicker | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Button Clicker section -->
    <section class="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="buttonclicker.title">Button Clicker</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="text-5xl font-bold mb-8 count-display">0</div>
                <button class="bg-blue-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-blue-700 transition-colors click-button" data-i18n="buttonclicker.click">CLICK ME</button>
                
                <div class="mt-8">
                    <button class="bg-gray-200 px-6 py-3 rounded-lg mr-4 reset-button" data-i18n="buttonclicker.reset">Reset</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="buttonclicker.content_title">About The Button Clicker</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="buttonclicker.content1">The Button Clicker is a simple yet satisfying tool that counts every click you make on the big blue button.</p>
                
                <p data-i18n="buttonclicker.content2">Use it to test your clicking speed, relieve stress, or just enjoy the simple pleasure of watching numbers go up.</p>
                
                <p data-i18n="buttonclicker.content3">With its responsive design, you can click away on any device, anytime, anywhere.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const clickBtn = document.querySelector('.click-button');
            const resetBtn = document.querySelector('.reset-button');
            const countDisplay = document.querySelector('.count-display');
            let count = 0;
            
            clickBtn.addEventListener('click', function() {
                count++;
                countDisplay.textContent = count;
            });
            
            resetBtn.addEventListener('click', function() {
                count = 0;
                countDisplay.textContent = count;
            });
        });
    </script>
</body>
</html>