<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free and accurate yes or no tarot readings. Get instant answers to your questions with our tarot oracle. 100% free and reliable.">
    <meta name="keywords" content="yes or no tarot, tarot accurate free, free tarot reading, yes no oracle, tarot decision">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-or-no-tarot-accurate.html">
    <title>Free Accurate Yes or No Tarot Oracle | Yes or No Answers | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .tarot-container {
            background: linear-gradient(135deg, #4c1d95 0%, #5b21b6 25%, #7c3aed 75%, #8b5cf6 100%);
            position: relative;
            overflow: hidden;
        }
        .tarot-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tarot-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="4" fill="white" opacity="0.1"/><circle cx="10" cy="10" r="1" fill="white" opacity="0.05"/><circle cx="30" cy="30" r="1" fill="white" opacity="0.05"/><path d="M15 15 L25 25 M25 15 L15 25" stroke="white" stroke-width="0.5" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23tarot-pattern)"/></svg>') repeat;
        }
        .tarot-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .card-flip {
            perspective: 1000px;
            width: 200px;
            height: 300px;
            margin: 0 auto;
        }
        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.8s;
            transform-style: preserve-3d;
        }
        .card-inner.flipped {
            transform: rotateY(180deg);
        }
        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            text-align: center;
        }
        .card-front {
            background: linear-gradient(135deg, #4c1d95, #7c3aed);
            color: white;
            border: 3px solid #fbbf24;
        }
        .card-back {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            transform: rotateY(180deg);
            border: 3px solid #4c1d95;
        }
        .reading-animation {
            animation: readingPulse 2s ease-in-out infinite;
        }
        @keyframes readingPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }
        .mystical-glow {
            box-shadow: 0 0 30px rgba(124, 58, 237, 0.5);
            animation: mysticalGlow 3s ease-in-out infinite alternate;
        }
        @keyframes mysticalGlow {
            from { box-shadow: 0 0 30px rgba(124, 58, 237, 0.5); }
            to { box-shadow: 0 0 50px rgba(139, 92, 246, 0.8); }
        }
        .tarot-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .preset-question {
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .preset-question:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
        }
        .card-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 1rem;
            max-width: 600px;
            margin: 0 auto;
        }
        .mini-card {
            width: 80px;
            height: 120px;
            background: linear-gradient(135deg, #4c1d95, #7c3aed);
            border: 2px solid #fbbf24;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .mini-card:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 25px rgba(124, 58, 237, 0.4);
        }
        .mini-card.selected {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            border-color: #4c1d95;
            transform: translateY(-10px) scale(1.1);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 tarot-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="tarot.title">Accurate Yes or No Tarot</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="tarot.subtitle">
                    Discover your destiny with ancient tarot wisdom. Get precise yes/no answers to life's questions.
                </p>

                <!-- Tarot Reading Interface -->
                <div class="max-w-4xl mx-auto tarot-card rounded-xl p-8 mb-8">
                    <!-- Question Input -->
                    <div class="mb-8">
                        <label for="question-input" class="block text-lg font-semibold mb-4" data-i18n="tarot.question_label">What question seeks an answer?</label>
                        <input type="text" id="question-input" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent text-center text-lg" placeholder="Ask the tarot your yes/no question..." data-i18n-placeholder="tarot.question_placeholder">
                    </div>

                    <!-- Card Selection Phase -->
                    <div id="card-selection-phase" class="mb-8">
                        <h3 class="text-xl font-semibold mb-6" data-i18n="tarot.select_card">Choose your tarot card with intuition</h3>
                        <div class="card-selection">
                            <div class="mini-card" data-card="1">1</div>
                            <div class="mini-card" data-card="2">2</div>
                            <div class="mini-card" data-card="3">3</div>
                            <div class="mini-card" data-card="4">4</div>
                            <div class="mini-card" data-card="5">5</div>
                            <div class="mini-card" data-card="6">6</div>
                            <div class="mini-card" data-card="7">7</div>
                        </div>
                        <p class="text-sm text-gray-600 mt-4" data-i18n="tarot.card_instruction">Trust your intuition and select the card that calls to you</p>
                    </div>

                    <!-- Main Tarot Card -->
                    <div id="main-card-container" class="hidden mb-8">
                        <div class="card-flip" id="tarot-card">
                            <div class="card-inner" id="card-inner">
                                <div class="card-front">
                                    <div class="text-center">
                                        <div class="text-4xl mb-4">🔮</div>
                                        <div class="text-lg font-bold">TAROT</div>
                                        <div class="text-sm">Oracle</div>
                                    </div>
                                </div>
                                <div class="card-back">
                                    <div id="card-result" class="text-center">
                                        <div id="card-symbol" class="text-6xl mb-4"></div>
                                        <div id="card-answer" class="text-2xl font-bold"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reading Result -->
                    <div id="reading-result" class="hidden mb-8">
                        <div class="bg-gradient-to-r from-purple-100 to-indigo-100 rounded-lg p-6">
                            <h3 class="text-2xl font-bold mb-4" data-i18n="tarot.your_reading">Your Tarot Reading</h3>
                            <div id="question-display" class="text-lg text-gray-700 mb-4 italic"></div>
                            <div id="tarot-interpretation" class="text-gray-700 mb-4"></div>
                            <div id="final-answer" class="text-3xl font-bold text-center"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4 mb-6">
                        <button id="draw-card-btn" class="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors font-semibold" data-i18n="tarot.draw_card">
                            Draw Card
                        </button>
                        <button id="new-reading-btn" class="hidden bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="tarot.new_reading">
                            New Reading
                        </button>
                    </div>

                    <!-- Statistics -->
                    <div class="tarot-stats rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="tarot.reading_stats">Tarot Reading Statistics</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div id="total-readings" class="text-3xl font-bold text-purple-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="tarot.total_readings">Total Readings</div>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="yes-readings" class="text-3xl font-bold text-green-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="tarot.yes_readings">Yes Answers</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div id="no-readings" class="text-3xl font-bold text-red-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="tarot.no_readings">No Answers</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div id="accuracy-rate" class="text-3xl font-bold text-blue-600">95%</div>
                                <div class="text-sm text-gray-600" data-i18n="tarot.accuracy">Accuracy Rate</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preset Questions -->
                <div class="max-w-4xl mx-auto mb-8">
                    <h3 class="text-xl font-semibold text-white mb-6" data-i18n="tarot.sample_questions">Popular Tarot Questions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="preset-questions">
                        <!-- Preset questions will be generated by JavaScript -->
                    </div>
                </div>

                <!-- Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="tarot.feature1">Ancient Wisdom</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="tarot.feature2">Accurate Readings</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="tarot.feature3">Free Forever</span>
                    </div>
                </div>
            </div>
        </section>

    <!-- How It Works -->
    <section id="how-it-works" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="how_it_works.title">How Our Tarot Oracle Works</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="how_it_works.subtitle">Our tarot-based decision system provides accurate yes/no answers in seconds:</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step1_title">Ask Your Question</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step1_desc">Focus on your dilemma. Our free tarot oracle works best with clear yes/no questions.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step2_title">Tarot Card Selection</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step2_desc">Our system selects tarot cards that represent your situation for accurate guidance.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step3_title">Receive Clear Answer</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step3_desc">Get an immediate yes or no answer based on tarot card interpretations.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section id="features" class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="features.title">Why Choose Our Tarot Oracle</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="features.subtitle">Key benefits of our free yes or no tarot service</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature1_title">Tarot-Based Accuracy</h3>
                        <p class="text-gray-600" data-i18n="features.feature1_desc">Get precise yes/no answers using authentic tarot card interpretations and symbolism.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature2_title">100% Free Readings</h3>
                        <p class="text-gray-600" data-i18n="features.feature2_desc">Unlimited yes or no tarot questions at no cost. No registration required.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature3_title">Decision History</h3>
                        <p class="text-gray-600" data-i18n="features.feature3_desc">Review past yes or no tarot readings to track patterns in your decisions.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w