<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free and accurate yes or no tarot readings. Get instant answers to your questions with our tarot oracle. 100% free and reliable.">
    <meta name="keywords" content="yes or no tarot, tarot accurate free, free tarot reading, yes no oracle, tarot decision">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-or-no-tarot-accurate">
    <title>Free Accurate Yes or No Tarot Oracle | Yes or No Answers | YesNoOracle.xyz</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-font-smoothing: antialiased;
            color: #1d1d1f;
        }
        .apple-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
        }
        .result-animation {
            animation: pulse 0.5s ease-in-out;
        }
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        .preset-question:hover {
            transform: translateY(-2px);
            transition: all 0.2s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }
        .language-selector {
            background: transparent;
            border: none;
            padding: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .language-selector:focus {
            outline: none;
            background: rgba(0, 113, 227, 0.1);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <span class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></span>
            </div>
            <nav class="hidden md:flex space-x-8">
                <a href="#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <button class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="apple-gradient py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <span data-i18n="hero.title_1">Free & Accurate</span> 
                <span class="text-primary">Yes or No Tarot</span> 
                <span data-i18n="hero.title_2">Answers</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-10" data-i18n="hero.subtitle">Get instant yes or no answers with our free tarot oracle. Accurate predictions for life's decisions.</p>
            
            <div class="max-w-xl mx-auto bg-white rounded-xl shadow-lg p-1 mb-12">
                <div class="flex">
                    <input type="text" id="question-input" placeholder="Ask your tarot question..." class="flex-grow px-4 py-3 rounded-l-xl focus:outline-none" data-i18n-placeholder="hero.placeholder">
                    <button id="generate-btn" class="bg-primary text-white px-6 py-3 rounded-r-xl hover:bg-blue-600 transition-colors" data-i18n="hero.button">Get Answer</button>
                </div>
                <div class="mt-4 text-left px-4">
                    <p class="text-gray-500 text-sm mb-2" data-i18n="hero.examples">Tarot question examples:</p>
                    <div class="flex flex-wrap gap-2" id="preset-questions-container">
                        <!-- Preset questions will be generated by JS -->
                    </div>
                </div>
            </div>
            
            <div id="result-container" class="hidden max-w-md mx-auto">
                <div class="bg-white rounded-xl shadow-lg p-8 mb-6">
                    <h2 class="text-2xl font-semibold mb-4" data-i18n="result.your_question">Your Tarot Question:</h2>
                    <p id="question-display" class="text-gray-700 mb-6 italic"></p>
                    <div class="flex justify-center">
                        <div id="result" class="text-6xl font-bold result-animation"></div>
                    </div>
                </div>
                <button id="try-again" class="text-primary underline hover:text-blue-600" data-i18n="result.try_again">Ask another question</button>
            </div>
            
            <div class="mt-12 flex justify-center space-x-4">
                <div class="flex items-center text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span data-i18n="stats.decisions">5,000+ Daily Tarot Readings</span>
                </div>
                <div class="flex items-center text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span data-i18n="stats.satisfaction">95% Accuracy Rate</span>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="how_it_works.title">How Our Tarot Oracle Works</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="how_it_works.subtitle">Our tarot-based decision system provides accurate yes/no answers in seconds:</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step1_title">Ask Your Question</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step1_desc">Focus on your dilemma. Our free tarot oracle works best with clear yes/no questions.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step2_title">Tarot Card Selection</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step2_desc">Our system selects tarot cards that represent your situation for accurate guidance.</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                        <span class="text-primary font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" data-i18n="how_it_works.step3_title">Receive Clear Answer</h3>
                    <p class="text-gray-600" data-i18n="how_it_works.step3_desc">Get an immediate yes or no answer based on tarot card interpretations.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section id="features" class="py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4" data-i18n="features.title">Why Choose Our Tarot Oracle</h2>
            <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="features.subtitle">Key benefits of our free yes or no tarot service</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature1_title">Tarot-Based Accuracy</h3>
                        <p class="text-gray-600" data-i18n="features.feature1_desc">Get precise yes/no answers using authentic tarot card interpretations and symbolism.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature2_title">100% Free Readings</h3>
                        <p class="text-gray-600" data-i18n="features.feature2_desc">Unlimited yes or no tarot questions at no cost. No registration required.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="features.feature3_title">Decision History</h3>
                        <p class="text-gray-600" data-i18n="features.feature3_desc">Review past yes or no tarot readings to track patterns in your decisions.</p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="mr-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w