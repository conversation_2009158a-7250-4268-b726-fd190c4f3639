# ⚡ 快速部署指南

## 🚀 最简单的部署方式 (推荐)

### 选项1: Vercel (免费 + 最简单)

```bash
# 1. 安装Vercel CLI
npm install -g vercel

# 2. 登录Vercel
vercel login

# 3. 在项目根目录运行
vercel --prod

# 4. 按照提示操作
# - Project name: yesnooracle
# - Framework: Other
# - Build command: (留空)
# - Output directory: ./

# 5. 设置自定义域名
vercel domains add yesnooracle.xyz
```

**完成！** 你的网站现在已经在线了！

### 选项2: 使用部署脚本

```bash
# 1. 给脚本执行权限
chmod +x deploy.sh

# 2. 部署到Vercel
./deploy.sh vercel

# 或部署到Netlify
./deploy.sh netlify
```

## 🌐 域名设置

### DNS配置
在你的域名提供商处设置：

```
类型    名称    值
A       @       *********** (Vercel IP)
CNAME   www     yesnooracle.xyz
```

### Vercel域名设置
```bash
# 添加域名
vercel domains add yesnooracle.xyz
vercel domains add www.yesnooracle.xyz

# 验证域名
vercel domains verify yesnooracle.xyz
```

## ✅ 部署后检查清单

访问以下页面确保都正常工作：

- [ ] https://yesnooracle.xyz/
- [ ] https://yesnooracle.xyz/button-maker.html
- [ ] https://yesnooracle.xyz/yes-no-button.html
- [ ] https://yesnooracle.xyz/yes-no-oracle.html
- [ ] https://yesnooracle.xyz/yes-no-oracle-accurate.html
- [ ] https://yesnooracle.xyz/yesno.html
- [ ] https://yesnooracle.xyz/no-button.html
- [ ] https://yesnooracle.xyz/button-clicker.html
- [ ] https://yesnooracle.xyz/game-buzzers.html
- [ ] https://yesnooracle.xyz/yes-and-no-button.html
- [ ] https://yesnooracle.xyz/no-or-yes-button.html
- [ ] https://yesnooracle.xyz/yes-or-no-tarot-accurate.html
- [ ] https://yesnooracle.xyz/yes-or-no-tarot-wheel.html
- [ ] https://yesnooracle.xyz/yes-or-mo.html

### 功能测试
- [ ] 所有按钮都能正常点击
- [ ] 动画效果正常播放
- [ ] 移动端响应式设计正常
- [ ] 统计功能正常工作
- [ ] 键盘快捷键正常工作

## 📊 SEO设置

### 1. Google Search Console
1. 访问 https://search.google.com/search-console
2. 添加属性: yesnooracle.xyz
3. 验证所有权
4. 提交sitemap: https://yesnooracle.xyz/sitemap.xml

### 2. Google Analytics (可选)
1. 创建GA4属性
2. 获取测量ID
3. 在每个HTML页面添加跟踪代码

### 3. 其他搜索引擎
- Bing Webmaster Tools
- Yandex Webmaster
- Baidu Search Console (如果需要中国市场)

## 🔧 性能优化

### Vercel自动优化
Vercel会自动提供：
- ✅ 全球CDN
- ✅ 自动压缩
- ✅ 图片优化
- ✅ SSL证书
- ✅ HTTP/2

### 额外优化建议
1. 启用Cloudflare (可选)
2. 压缩图片资源
3. 监控Core Web Vitals

## 🆘 常见问题

### Q: 部署后页面显示404
A: 检查文件路径是否正确，确保所有HTML文件都在根目录

### Q: CSS/JS文件加载失败
A: 检查`src/`目录是否正确上传，路径是否正确

### Q: 域名无法访问
A: 检查DNS设置，等待DNS传播（最多48小时）

### Q: SSL证书问题
A: Vercel会自动提供SSL证书，如果有问题联系Vercel支持

## 📞 支持

如果遇到问题：
1. 检查Vercel/Netlify控制台的部署日志
2. 查看浏览器开发者工具的错误信息
3. 参考完整的DEPLOYMENT-GUIDE.md

---

🎉 **恭喜！你的YesNoOracle.xyz网站现在已经上线了！**

记得分享给朋友们试用！ 🚀
