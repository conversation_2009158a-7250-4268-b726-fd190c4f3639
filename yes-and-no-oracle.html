<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes and No Oracle - Get guidance for your decisions">
    <title>Yes and No Oracle | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Oracle section -->
    <section class="py-20 bg-gradient-to-br from-purple-50 to-indigo-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="yesandnooracle.title">Yes and No Oracle</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="mb-8">
                    <textarea class="w-full p-4 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="Ask your question..." rows="4" data-i18n="yesandnooracle.question_placeholder"></textarea>
                </div>
                
                <button class="bg-purple-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-purple-700 transition-colors ask-button" data-i18n="yesandnooracle.ask">ASK THE ORACLE</button>
                
                <div class="result-area hidden mt-8">
                    <p class="text-5xl font-bold result-text" style="color: #7c3aed;"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="yesandnooracle.content_title">About The Yes and No Oracle</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="yesandnooracle.content1">The Yes and No Oracle provides guidance for your decisions with simple yet profound answers.</p>
                
                <p data-i18n="yesandnooracle.content2">Whether you're facing a difficult choice or just need some direction, ask your question and receive a clear yes or no response.</p>
                
                <p data-i18n="yesandnooracle.content3">Our oracle combines ancient wisdom with modern technology to help guide your path.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const askBtn = document.querySelector('.ask-button');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            askBtn.addEventListener('click', function() {
                const answers = ['YES', 'NO', 'MAYBE', 'TRY AGAIN LATER'];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                
                resultText.textContent = randomAnswer;
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>