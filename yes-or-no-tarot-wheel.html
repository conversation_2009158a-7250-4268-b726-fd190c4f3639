<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or No Tarot Wheel - Spin the wheel for tarot answers">
    <title>Yes or No Tarot Wheel | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Tarot Wheel section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="tarotwheel.title">Yes or No Tarot Wheel</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="relative h-64 w-64 mx-auto mb-8">
                    <div class="tarot-wheel absolute inset-0 rounded-full border-8 border-purple-600 overflow-hidden transition-transform duration-3000 ease-out">
                        <div class="wheel-segment absolute w-full h-full" style="transform: rotate(0deg)">
                            <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-16 bg-red-500"></div>
                            <p class="absolute top-8 left-1/2 transform -translate-x-1/2 text-sm font-bold">YES</p>
                        </div>
                        <!-- More segments would be added here -->
                    </div>
                    <button class="spin-button absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center font-bold shadow-lg hover:bg-purple-700" data-i18n="tarotwheel.spin_button">SPIN</button>
                </div>
                
                <div class="result-area hidden mt-8">
                    <p class="text-3xl font-bold result-text" style="color: #7c3aed;"></p>
                    <p class="text-lg mt-2 result-details"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="tarotwheel.content_title">About The Yes or No Tarot Wheel</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="tarotwheel.content1">Our tarot wheel provides a fun and interactive way to get yes or no answers.</p>
                
                <p data-i18n="tarotwheel.content2">Spin the wheel to receive guidance based on traditional tarot interpretations.</p>
                
                <p data-i18n="tarotwheel.content3">Perfect for quick decisions when you need a clear yes or no response.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const wheel = document.querySelector('.tarot-wheel');
            const spinBtn = document.querySelector('.spin-button');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            const resultDetails = document.querySelector('.result-details');
            
            spinBtn.addEventListener('click', function() {
                // Disable button during spin
                spinBtn.disabled = true;
                
                // Random spin duration and degree
                const spinDuration = 3000 + Math.random() * 2000;
                const spinDegrees = 1800 + Math.random() * 1800;
                
                // Apply spin animation
                wheel.style.transition = `transform ${spinDuration}ms cubic-bezier(0.17, 0.67, 0.21, 0.99)`;
                wheel.style.transform = `rotate(${spinDegrees}deg)`;
                
                // Determine result after spin
                setTimeout(function() {
                    const normalizedDegree = spinDegrees % 360;
                    let result = '';
                    let details = '';
                    
                    if (normalizedDegree < 180) {
                        result = 'YES';
                        details = 'The cards favor a positive outcome';
                    } else {
                        result = 'NO';
                        details = 'The cards suggest reconsideration';
                    }
                    
                    resultText.textContent = result;
                    resultDetails.textContent = details;
                    resultArea.classList.remove('hidden');
                    
                    // Re-enable button
                    spinBtn.disabled = false;
                }, spinDuration);
            });
        });
    </script>
</body>
</html>