<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or No Tarot Wheel - Spin the mystical wheel for instant tarot guidance and answers">
    <meta name="keywords" content="tarot wheel, yes no tarot, tarot spinner, mystical wheel, fortune wheel">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-or-no-tarot-wheel.html">
    <title>Yes or No Tarot Wheel - Mystical Fortune Spinner | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .wheel-container {
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 25%, #6366f1 75%, #8b5cf6 100%);
            position: relative;
            overflow: hidden;
        }
        .wheel-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="wheel-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="5" fill="white" opacity="0.1"/><circle cx="10" cy="10" r="1" fill="white" opacity="0.05"/><circle cx="40" cy="40" r="1" fill="white" opacity="0.05"/><path d="M20 20 L30 30 M30 20 L20 30" stroke="white" stroke-width="0.5" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23wheel-pattern)"/></svg>') repeat;
        }
        .wheel-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .tarot-wheel {
            width: 320px;
            height: 320px;
            position: relative;
            margin: 0 auto;
            transition: transform 4s cubic-bezier(0.23, 1, 0.32, 1);
        }
        .wheel-segment {
            position: absolute;
            width: 50%;
            height: 50%;
            transform-origin: 100% 100%;
            overflow: hidden;
        }
        .wheel-segment:nth-child(1) { transform: rotate(0deg); background: linear-gradient(45deg, #ef4444, #dc2626); }
        .wheel-segment:nth-child(2) { transform: rotate(45deg); background: linear-gradient(45deg, #10b981, #059669); }
        .wheel-segment:nth-child(3) { transform: rotate(90deg); background: linear-gradient(45deg, #f59e0b, #d97706); }
        .wheel-segment:nth-child(4) { transform: rotate(135deg); background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        .wheel-segment:nth-child(5) { transform: rotate(180deg); background: linear-gradient(45deg, #06b6d4, #0891b2); }
        .wheel-segment:nth-child(6) { transform: rotate(225deg); background: linear-gradient(45deg, #ec4899, #db2777); }
        .wheel-segment:nth-child(7) { transform: rotate(270deg); background: linear-gradient(45deg, #84cc16, #65a30d); }
        .wheel-segment:nth-child(8) { transform: rotate(315deg); background: linear-gradient(45deg, #6366f1, #4f46e5); }

        .segment-content {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-weight: bold;
            text-align: center;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        .segment-symbol {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .wheel-pointer {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 30px solid #fbbf24;
            z-index: 10;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .spin-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            border: 4px solid white;
            color: white;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 20;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        .spin-button:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 12px 35px rgba(0,0,0,0.4);
        }
        .spin-button:active {
            transform: translate(-50%, -50%) scale(0.95);
        }
        .spin-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: translate(-50%, -50%) scale(1);
        }

        .spinning {
            animation: sparkle 0.5s ease-in-out infinite alternate;
        }
        @keyframes sparkle {
            from { box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 20px rgba(251, 191, 36, 0.5); }
            to { box-shadow: 0 12px 35px rgba(0,0,0,0.4), 0 0 30px rgba(251, 191, 36, 0.8); }
        }

        .result-display {
            animation: resultReveal 0.8s ease-out;
        }
        @keyframes resultReveal {
            0% { transform: scale(0) rotate(180deg); opacity: 0; }
            50% { transform: scale(1.2) rotate(90deg); opacity: 0.7; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }

        .wheel-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }

        .mystical-mode {
            background: linear-gradient(135deg, #1e1b4b, #312e81);
            animation: mysticalPulse 4s ease-in-out infinite alternate;
        }
        @keyframes mysticalPulse {
            from { box-shadow: 0 0 30px rgba(99, 102, 241, 0.3); }
            to { box-shadow: 0 0 50px rgba(139, 92, 246, 0.6); }
        }

        .fortune-history {
            max-height: 300px;
            overflow-y: auto;
        }
        .fortune-item {
            transition: all 0.2s ease;
        }
        .fortune-item:hover {
            background: rgba(99, 102, 241, 0.1);
            transform: translateX(5px);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 wheel-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="wheel.title">Mystical Tarot Wheel</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="wheel.subtitle">
                    Spin the wheel of fortune and let the ancient tarot energies guide your destiny.
                </p>

                <!-- Tarot Wheel Interface -->
                <div class="max-w-4xl mx-auto wheel-card rounded-xl p-8 mb-8">
                    <!-- Question Input -->
                    <div class="mb-8">
                        <label for="question-input" class="block text-lg font-semibold mb-4" data-i18n="wheel.question_label">What question seeks the wheel's wisdom?</label>
                        <input type="text" id="question-input" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-center text-lg" placeholder="Ask your question to the mystical wheel..." data-i18n-placeholder="wheel.question_placeholder">
                    </div>

                    <!-- Tarot Wheel -->
                    <div class="relative mb-8">
                        <div class="wheel-pointer"></div>
                        <div class="tarot-wheel" id="tarot-wheel">
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">🌟</div>
                                    <div>YES</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">❌</div>
                                    <div>NO</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">✨</div>
                                    <div>MAYBE</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">🔮</div>
                                    <div>UNCLEAR</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">⭐</div>
                                    <div>STRONG YES</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">🚫</div>
                                    <div>STRONG NO</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">⏳</div>
                                    <div>WAIT</div>
                                </div>
                            </div>
                            <div class="wheel-segment">
                                <div class="segment-content">
                                    <div class="segment-symbol">🎯</div>
                                    <div>FOCUS</div>
                                </div>
                            </div>
                        </div>
                        <button id="spin-button" class="spin-button" data-i18n="wheel.spin">SPIN</button>
                    </div>

                    <!-- Result Display -->
                    <div id="result-area" class="hidden mb-8">
                        <div class="result-display bg-gradient-to-r from-indigo-100 to-purple-100 rounded-lg p-6">
                            <h3 class="text-2xl font-bold mb-4" data-i18n="wheel.your_fortune">Your Fortune</h3>
                            <div id="question-display" class="text-lg text-gray-700 mb-4 italic"></div>
                            <div id="wheel-result" class="text-4xl font-bold mb-4"></div>
                            <div id="fortune-message" class="text-gray-700 mb-4"></div>
                            <div id="tarot-guidance" class="text-sm text-gray-600 italic"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4 mb-6">
                        <button id="spin-again-btn" class="hidden bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors" data-i18n="wheel.spin_again">
                            Spin Again
                        </button>
                        <button id="mystical-mode-btn" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors" data-i18n="wheel.mystical_mode">
                            Mystical Mode
                        </button>
                        <button id="clear-history-btn" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="wheel.clear_history">
                            Clear History
                        </button>
                    </div>

                    <!-- Statistics -->
                    <div class="wheel-stats rounded-lg p-6 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="wheel.wheel_stats">Wheel of Fortune Statistics</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="total-spins" class="text-3xl font-bold text-green-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="wheel.total_spins">Total Spins</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div id="yes-count" class="text-3xl font-bold text-blue-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="wheel.yes_results">Yes Results</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div id="no-count" class="text-3xl font-bold text-red-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="wheel.no_results">No Results</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div id="mystical-spins" class="text-3xl font-bold text-purple-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="wheel.mystical_spins">Mystical Spins</div>
                            </div>
                        </div>
                    </div>

                    <!-- Fortune History -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="wheel.fortune_history">Fortune History</h3>
                        <div id="fortune-history" class="fortune-history space-y-2">
                            <p class="text-gray-500 text-center" data-i18n="wheel.no_history">No fortunes yet. Spin the wheel to begin your journey!</p>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="wheel.feature1">8 Fortune Outcomes</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="wheel.feature2">Mystical Animations</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="wheel.feature3">Fortune History</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="wheel.content_title">About the Mystical Tarot Wheel</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="wheel.content_subtitle">
                    Experience the ancient art of divination through our interactive wheel of fortune.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-indigo-100">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="wheel.feature1_title">8 Fortune Outcomes</h3>
                        <p class="text-gray-600" data-i18n="wheel.feature1_desc">From strong yes to wait, our wheel offers nuanced guidance beyond simple yes/no answers.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="wheel.feature2_title">Mystical Experience</h3>
                        <p class="text-gray-600" data-i18n="wheel.feature2_desc">Immerse yourself in mystical animations and atmospheric design that enhances your spiritual journey.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-pink-100">
                        <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="wheel.feature3_title">Fortune Tracking</h3>
                        <p class="text-gray-600" data-i18n="wheel.feature3_desc">Keep track of your spiritual journey with detailed statistics and fortune history.</p>
                    </div>
                </div>

                <!-- Wheel Segments Explanation -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="wheel.segments_title">Understanding the Wheel Segments</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <span class="text-2xl mr-3">🌟</span>
                                <div>
                                    <span class="font-semibold text-green-700">YES</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.yes_desc">Positive energy surrounds your question</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <span class="text-2xl mr-3">⭐</span>
                                <div>
                                    <span class="font-semibold text-blue-700">STRONG YES</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.strong_yes_desc">Overwhelming positive forces</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                <span class="text-2xl mr-3">✨</span>
                                <div>
                                    <span class="font-semibold text-yellow-700">MAYBE</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.maybe_desc">Uncertain energies, proceed with caution</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                <span class="text-2xl mr-3">🎯</span>
                                <div>
                                    <span class="font-semibold text-purple-700">FOCUS</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.focus_desc">Concentrate your intentions</p>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-red-50 rounded-lg">
                                <span class="text-2xl mr-3">❌</span>
                                <div>
                                    <span class="font-semibold text-red-700">NO</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.no_desc">Negative forces advise against</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-red-50 rounded-lg">
                                <span class="text-2xl mr-3">🚫</span>
                                <div>
                                    <span class="font-semibold text-red-700">STRONG NO</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.strong_no_desc">Powerful opposition to your path</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-2xl mr-3">🔮</span>
                                <div>
                                    <span class="font-semibold text-gray-700">UNCLEAR</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.unclear_desc">The future remains veiled</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                                <span class="text-2xl mr-3">⏳</span>
                                <div>
                                    <span class="font-semibold text-orange-700">WAIT</span>
                                    <p class="text-sm text-gray-600" data-i18n="wheel.wait_desc">Patience will reveal the answer</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let totalSpins = 0;
            let yesCount = 0;
            let noCount = 0;
            let mysticalSpins = 0;
            let mysticalMode = false;
            let fortuneHistory = [];
            let isSpinning = false;

            // DOM elements
            const wheel = document.getElementById('tarot-wheel');
            const spinButton = document.getElementById('spin-button');
            const questionInput = document.getElementById('question-input');
            const resultArea = document.getElementById('result-area');
            const questionDisplay = document.getElementById('question-display');
            const wheelResult = document.getElementById('wheel-result');
            const fortuneMessage = document.getElementById('fortune-message');
            const tarotGuidance = document.getElementById('tarot-guidance');
            const spinAgainBtn = document.getElementById('spin-again-btn');
            const mysticalModeBtn = document.getElementById('mystical-mode-btn');
            const clearHistoryBtn = document.getElementById('clear-history-btn');

            // Statistics elements
            const totalSpinsDisplay = document.getElementById('total-spins');
            const yesCountDisplay = document.getElementById('yes-count');
            const noCountDisplay = document.getElementById('no-count');
            const mysticalSpinsDisplay = document.getElementById('mystical-spins');
            const fortuneHistoryContainer = document.getElementById('fortune-history');

            // Wheel segments data
            const wheelSegments = [
                {
                    name: 'YES',
                    symbol: '🌟',
                    color: '#10b981',
                    message: 'The universe aligns in your favor. Positive energies surround your question.',
                    guidance: 'Trust in the path ahead and move forward with confidence.'
                },
                {
                    name: 'NO',
                    symbol: '❌',
                    color: '#ef4444',
                    message: 'The cosmic forces advise against this path. Reconsider your approach.',
                    guidance: 'Sometimes the greatest wisdom lies in knowing when to step back.'
                },
                {
                    name: 'MAYBE',
                    symbol: '✨',
                    color: '#f59e0b',
                    message: 'The energies are mixed. Proceed with careful consideration.',
                    guidance: 'Gather more information before making your final decision.'
                },
                {
                    name: 'UNCLEAR',
                    symbol: '🔮',
                    color: '#8b5cf6',
                    message: 'The mystical veil obscures the answer. The time is not yet right.',
                    guidance: 'Patience will reveal what is hidden. Trust in divine timing.'
                },
                {
                    name: 'STRONG YES',
                    symbol: '⭐',
                    color: '#06b6d4',
                    message: 'Overwhelming positive forces support your endeavor. The stars align perfectly.',
                    guidance: 'This is your moment. Embrace the opportunity with full confidence.'
                },
                {
                    name: 'STRONG NO',
                    symbol: '🚫',
                    color: '#dc2626',
                    message: 'Powerful opposition blocks this path. The universe strongly advises against it.',
                    guidance: 'Heed this warning and seek alternative routes to your goal.'
                },
                {
                    name: 'WAIT',
                    symbol: '⏳',
                    color: '#84cc16',
                    message: 'The timing is not optimal. Patience will bring better opportunities.',
                    guidance: 'All things come to those who wait for the right moment.'
                },
                {
                    name: 'FOCUS',
                    symbol: '🎯',
                    color: '#6366f1',
                    message: 'Concentrate your intentions and clarify your true desires.',
                    guidance: 'The answer lies within. Meditate on what you truly seek.'
                }
            ];

            // Update statistics display
            function updateStatistics() {
                totalSpinsDisplay.textContent = totalSpins;
                yesCountDisplay.textContent = yesCount;
                noCountDisplay.textContent = noCount;
                mysticalSpinsDisplay.textContent = mysticalSpins;
            }

            // Add fortune to history
            function addToHistory(question, result, message) {
                const timestamp = new Date().toLocaleTimeString();
                const fortune = {
                    question: question || 'Anonymous question',
                    result: result,
                    message: message,
                    timestamp: timestamp
                };

                fortuneHistory.unshift(fortune);
                if (fortuneHistory.length > 10) {
                    fortuneHistory.pop();
                }

                updateHistoryDisplay();
            }

            // Update history display
            function updateHistoryDisplay() {
                if (fortuneHistory.length === 0) {
                    fortuneHistoryContainer.innerHTML = '<p class="text-gray-500 text-center">No fortunes yet. Spin the wheel to begin your journey!</p>';
                    return;
                }

                fortuneHistoryContainer.innerHTML = fortuneHistory.map(fortune => `
                    <div class="fortune-item p-3 bg-white rounded-lg border border-gray-200">
                        <div class="flex justify-between items-start mb-2">
                            <span class="font-semibold text-sm" style="color: ${getSegmentByName(fortune.result).color}">${fortune.result}</span>
                            <span class="text-xs text-gray-500">${fortune.timestamp}</span>
                        </div>
                        <p class="text-sm text-gray-700 mb-1">"${fortune.question}"</p>
                        <p class="text-xs text-gray-600">${fortune.message}</p>
                    </div>
                `).join('');
            }

            // Get segment by name
            function getSegmentByName(name) {
                return wheelSegments.find(segment => segment.name === name) || wheelSegments[0];
            }

            // Spin the wheel
            function spinWheel() {
                if (isSpinning) return;

                const question = questionInput.value.trim();
                if (!question) {
                    alert('Please enter your question first.');
                    questionInput.focus();
                    return;
                }

                isSpinning = true;
                spinButton.disabled = true;
                spinButton.classList.add('spinning');

                // Hide previous result
                resultArea.classList.add('hidden');
                spinAgainBtn.classList.add('hidden');

                // Random spin parameters
                const spinDuration = 3000 + Math.random() * 2000;
                const baseRotation = 1800 + Math.random() * 1800; // Multiple full rotations
                const segmentAngle = 360 / wheelSegments.length;
                const randomSegment = Math.floor(Math.random() * wheelSegments.length);
                const targetAngle = randomSegment * segmentAngle + (segmentAngle / 2);
                const finalRotation = baseRotation + (360 - targetAngle);

                // Apply spin animation
                wheel.style.transition = `transform ${spinDuration}ms cubic-bezier(0.23, 1, 0.32, 1)`;
                wheel.style.transform = `rotate(${finalRotation}deg)`;

                // Determine result after spin
                setTimeout(() => {
                    const selectedSegment = wheelSegments[randomSegment];

                    // Update statistics
                    totalSpins++;
                    if (mysticalMode) mysticalSpins++;

                    if (selectedSegment.name.includes('YES')) {
                        yesCount++;
                    } else if (selectedSegment.name.includes('NO')) {
                        noCount++;
                    }

                    // Display result
                    questionDisplay.textContent = `"${question}"`;
                    wheelResult.textContent = selectedSegment.symbol + ' ' + selectedSegment.name;
                    wheelResult.style.color = selectedSegment.color;
                    fortuneMessage.textContent = selectedSegment.message;
                    tarotGuidance.textContent = selectedSegment.guidance;

                    // Show result
                    resultArea.classList.remove('hidden');
                    spinAgainBtn.classList.remove('hidden');

                    // Add to history
                    addToHistory(question, selectedSegment.name, selectedSegment.message);

                    // Update statistics
                    updateStatistics();

                    // Reset spin state
                    isSpinning = false;
                    spinButton.disabled = false;
                    spinButton.classList.remove('spinning');

                    // Clear question for next spin
                    questionInput.value = '';

                }, spinDuration);
            }

            // Reset for new spin
            function spinAgain() {
                resultArea.classList.add('hidden');
                spinAgainBtn.classList.add('hidden');
                questionInput.focus();
            }

            // Toggle mystical mode
            function toggleMysticalMode() {
                mysticalMode = !mysticalMode;

                if (mysticalMode) {
                    mysticalModeBtn.textContent = 'Exit Mystical Mode';
                    mysticalModeBtn.classList.remove('bg-purple-600', 'hover:bg-purple-700');
                    mysticalModeBtn.classList.add('bg-orange-500', 'hover:bg-orange-600');

                    // Add mystical styling
                    document.querySelector('.wheel-card').classList.add('mystical-mode');

                } else {
                    mysticalModeBtn.textContent = 'Mystical Mode';
                    mysticalModeBtn.classList.remove('bg-orange-500', 'hover:bg-orange-600');
                    mysticalModeBtn.classList.add('bg-purple-600', 'hover:bg-purple-700');

                    // Remove mystical styling
                    document.querySelector('.wheel-card').classList.remove('mystical-mode');
                }
            }

            // Clear history
            function clearHistory() {
                if (confirm('Are you sure you want to clear your fortune history?')) {
                    fortuneHistory = [];
                    totalSpins = 0;
                    yesCount = 0;
                    noCount = 0;
                    mysticalSpins = 0;
                    updateStatistics();
                    updateHistoryDisplay();
                }
            }

            // Event listeners
            spinButton.addEventListener('click', spinWheel);
            spinAgainBtn.addEventListener('click', spinAgain);
            mysticalModeBtn.addEventListener('click', toggleMysticalMode);
            clearHistoryBtn.addEventListener('click', clearHistory);

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && questionInput.value.trim() && !isSpinning) {
                    spinWheel();
                }

                if (e.key === ' ' && !isSpinning) {
                    e.preventDefault();
                    spinWheel();
                }

                if (e.key === 'r' || e.key === 'R') {
                    e.preventDefault();
                    spinAgain();
                }

                if (e.key === 'm' || e.key === 'M') {
                    e.preventDefault();
                    toggleMysticalMode();
                }
            });

            // Initialize
            updateStatistics();
            updateHistoryDisplay();
        });
    </script>
</body>
</html>