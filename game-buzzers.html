<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Game Buzzers - Fun buzzer games for parties and events">
    <title>Game Buzzers | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Buzzer game section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="buzzers.title">Game Buzzers</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <button class="buzzer-btn bg-red-500 hover:bg-red-600 text-white font-bold py-8 px-4 rounded-lg text-xl transition-colors" data-sound="buzzer1" data-i18n="buzzers.buzzer1">Buzzer 1</button>
                    <button class="buzzer-btn bg-blue-500 hover:bg-blue-600 text-white font-bold py-8 px-4 rounded-lg text-xl transition-colors" data-sound="buzzer2" data-i18n="buzzers.buzzer2">Buzzer 2</button>
                    <button class="buzzer-btn bg-green-500 hover:bg-green-600 text-white font-bold py-8 px-4 rounded-lg text-xl transition-colors" data-sound="buzzer3" data-i18n="buzzers.buzzer3">Buzzer 3</button>
                    <button class="buzzer-btn bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-8 px-4 rounded-lg text-xl transition-colors" data-sound="buzzer4" data-i18n="buzzers.buzzer4">Buzzer 4</button>
                </div>
                
                <div class="result-area mt-8">
                    <p class="text-2xl font-bold last-pressed" data-i18n="buzzers.last_pressed">Last pressed: None</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="buzzers.content_title">About Game Buzzers</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="buzzers.content1">Our game buzzers provide fun interactive sounds for quiz games and competitions.</p>
                
                <p data-i18n="buzzers.content2">Perfect for classroom activities, game shows, or family game nights.</p>
                
                <p data-i18n="buzzers.content3">Each buzzer produces a unique sound to identify which player answered first.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const buzzerBtns = document.querySelectorAll('.buzzer-btn');
            const lastPressed = document.querySelector('.last-pressed');
            
            buzzerBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const buzzerName = btn.textContent;
                    lastPressed.textContent = `Last pressed: ${buzzerName}`;
                    
                    // Play buzzer sound (would need actual sound files)
                    // new Audio(`sounds/${btn.dataset.sound}.mp3`).play();
                });
            });
        });
    </script>
</body>
</html>