<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Game Buzzers - Virtual game show buzzers for quizzes and competitions">
    <meta name="keywords" content="game buzzers, quiz buzzers, game show, competition tool">
    <link rel="canonical" href="https://yesnooracle.xyz/game-buzzers.html">
    <title>Game Buzzers - Quiz Show Buzzers | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .buzzers-container {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
            position: relative;
            overflow: hidden;
        }
        .buzzers-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="buzzer-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="3" fill="white" opacity="0.1"/><circle cx="5" cy="5" r="1" fill="white" opacity="0.05"/><circle cx="25" cy="25" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23buzzer-pattern)"/></svg>') repeat;
        }
        .game-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .buzzer-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .buzzer-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }
        .buzzer-btn:active {
            transform: scale(0.95);
        }
        .buzzer-btn.pressed {
            animation: buzzerPress 0.6s ease-out;
        }
        @keyframes buzzerPress {
            0% { transform: scale(1); }
            25% { transform: scale(0.9); }
            50% { transform: scale(1.1); }
            75% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .buzzer-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }
        .buzzer-btn.pressed::before {
            width: 300px;
            height: 300px;
        }
        .winner-display {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: winnerGlow 2s ease-in-out infinite alternate;
        }
        @keyframes winnerGlow {
            from { filter: drop-shadow(0 0 10px #fbbf24); }
            to { filter: drop-shadow(0 0 20px #f59e0b); }
        }
        .scoreboard {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .player-score {
            transition: all 0.3s ease;
        }
        .player-score.winner {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            transform: scale(1.05);
        }
        .sound-wave {
            display: inline-block;
            width: 4px;
            height: 20px;
            background: currentColor;
            margin: 0 1px;
            animation: soundWave 1s ease-in-out infinite;
        }
        .sound-wave:nth-child(2) { animation-delay: 0.1s; }
        .sound-wave:nth-child(3) { animation-delay: 0.2s; }
        .sound-wave:nth-child(4) { animation-delay: 0.3s; }
        @keyframes soundWave {
            0%, 100% { height: 20px; }
            50% { height: 5px; }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 buzzers-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="buzzers.title">Game Show Buzzers</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="buzzers.subtitle">
                    Bring the excitement of game shows to your quiz nights, classrooms, and competitions!
                </p>

                <!-- Game Interface -->
                <div class="max-w-6xl mx-auto game-card rounded-xl p-8 mb-8">
                    <!-- Game Controls -->
                    <div class="flex justify-center space-x-4 mb-8">
                        <button id="start-game" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors font-semibold" data-i18n="buzzers.start_game">
                            Start Game
                        </button>
                        <button id="reset-game" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors font-semibold" data-i18n="buzzers.reset_game">
                            Reset
                        </button>
                        <button id="settings-btn" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors font-semibold" data-i18n="buzzers.settings">
                            Settings
                        </button>
                    </div>

                    <!-- Winner Display -->
                    <div id="winner-display" class="hidden mb-8">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 mb-4">
                            <h2 class="text-3xl font-bold text-white mb-2" data-i18n="buzzers.winner">🏆 Winner!</h2>
                            <p id="winner-text" class="text-xl text-white"></p>
                            <p id="winner-time" class="text-lg text-yellow-100 mt-2"></p>
                        </div>
                    </div>

                    <!-- Buzzers Grid -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="text-center">
                            <button id="buzzer-1" class="buzzer-btn bg-gradient-to-br from-red-500 to-red-700 text-white font-bold py-12 px-6 rounded-xl text-2xl w-full" data-player="1" data-color="red" data-key="1">
                                <div class="flex flex-col items-center">
                                    <span class="text-4xl mb-2">🔴</span>
                                    <span data-i18n="buzzers.player">Player</span> 1
                                    <div class="sound-indicators hidden mt-2">
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                    </div>
                                </div>
                            </button>
                            <p class="text-sm text-gray-600 mt-2" data-i18n="buzzers.key_hint">Press '1' key</p>
                        </div>

                        <div class="text-center">
                            <button id="buzzer-2" class="buzzer-btn bg-gradient-to-br from-blue-500 to-blue-700 text-white font-bold py-12 px-6 rounded-xl text-2xl w-full" data-player="2" data-color="blue" data-key="2">
                                <div class="flex flex-col items-center">
                                    <span class="text-4xl mb-2">🔵</span>
                                    <span data-i18n="buzzers.player">Player</span> 2
                                    <div class="sound-indicators hidden mt-2">
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                    </div>
                                </div>
                            </button>
                            <p class="text-sm text-gray-600 mt-2" data-i18n="buzzers.key_hint">Press '2' key</p>
                        </div>

                        <div class="text-center">
                            <button id="buzzer-3" class="buzzer-btn bg-gradient-to-br from-green-500 to-green-700 text-white font-bold py-12 px-6 rounded-xl text-2xl w-full" data-player="3" data-color="green" data-key="3">
                                <div class="flex flex-col items-center">
                                    <span class="text-4xl mb-2">🟢</span>
                                    <span data-i18n="buzzers.player">Player</span> 3
                                    <div class="sound-indicators hidden mt-2">
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                    </div>
                                </div>
                            </button>
                            <p class="text-sm text-gray-600 mt-2" data-i18n="buzzers.key_hint">Press '3' key</p>
                        </div>

                        <div class="text-center">
                            <button id="buzzer-4" class="buzzer-btn bg-gradient-to-br from-yellow-500 to-yellow-700 text-white font-bold py-12 px-6 rounded-xl text-2xl w-full" data-player="4" data-color="yellow" data-key="4">
                                <div class="flex flex-col items-center">
                                    <span class="text-4xl mb-2">🟡</span>
                                    <span data-i18n="buzzers.player">Player</span> 4
                                    <div class="sound-indicators hidden mt-2">
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                        <div class="sound-wave"></div>
                                    </div>
                                </div>
                            </button>
                            <p class="text-sm text-gray-600 mt-2" data-i18n="buzzers.key_hint">Press '4' key</p>
                        </div>
                    </div>

                    <!-- Scoreboard -->
                    <div class="scoreboard rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="buzzers.scoreboard">Scoreboard</h3>
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="player-score text-center p-4 rounded-lg bg-white">
                                <div class="text-2xl mb-1">🔴</div>
                                <div class="font-semibold" data-i18n="buzzers.player">Player 1</div>
                                <div id="score-1" class="text-2xl font-bold text-red-600">0</div>
                            </div>
                            <div class="player-score text-center p-4 rounded-lg bg-white">
                                <div class="text-2xl mb-1">🔵</div>
                                <div class="font-semibold" data-i18n="buzzers.player">Player 2</div>
                                <div id="score-2" class="text-2xl font-bold text-blue-600">0</div>
                            </div>
                            <div class="player-score text-center p-4 rounded-lg bg-white">
                                <div class="text-2xl mb-1">🟢</div>
                                <div class="font-semibold" data-i18n="buzzers.player">Player 3</div>
                                <div id="score-3" class="text-2xl font-bold text-green-600">0</div>
                            </div>
                            <div class="player-score text-center p-4 rounded-lg bg-white">
                                <div class="text-2xl mb-1">🟡</div>
                                <div class="font-semibold" data-i18n="buzzers.player">Player 4</div>
                                <div id="score-4" class="text-2xl font-bold text-yellow-600">0</div>
                            </div>
                        </div>
                    </div>

                    <!-- Game Status -->
                    <div class="mt-6 text-center">
                        <p id="game-status" class="text-lg font-medium text-gray-600" data-i18n="buzzers.ready_to_play">Ready to play! Press "Start Game" to begin.</p>
                        <p id="round-info" class="text-sm text-gray-500 mt-2 hidden">Round <span id="current-round">1</span> of <span id="total-rounds">5</span></p>
                    </div>
                </div>

                <!-- Game Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buzzers.feature1">Keyboard Support</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buzzers.feature2">Sound Effects</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="buzzers.feature3">Score Tracking</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="buzzers.content_title">About Game Show Buzzers</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="buzzers.content_subtitle">
                    Transform any quiz into an exciting game show experience with our interactive buzzer system.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-red-100">
                        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buzzers.feature1_title">Realistic Sound Effects</h3>
                        <p class="text-gray-600" data-i18n="buzzers.feature1_desc">Each buzzer produces unique sound effects that simulate real game show buzzers for an authentic experience.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-blue-100">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buzzers.feature2_title">Score Tracking</h3>
                        <p class="text-gray-600" data-i18n="buzzers.feature2_desc">Built-in scoreboard keeps track of each player's points throughout the game session.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-green-100">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="buzzers.feature3_title">Multi-Platform</h3>
                        <p class="text-gray-600" data-i18n="buzzers.feature3_desc">Works perfectly on desktop, tablet, and mobile devices. Support for both touch and keyboard input.</p>
                    </div>
                </div>

                <!-- How to Use -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="buzzers.how_to_use">How to Use Game Buzzers</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="buzzers.setup_title">Game Setup</h4>
                            <ol class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                    <span data-i18n="buzzers.setup1">Click "Start Game" to begin a new session</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                    <span data-i18n="buzzers.setup2">Assign each player to a colored buzzer</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                    <span data-i18n="buzzers.setup3">Players can use mouse/touch or keyboard (1-4 keys)</span>
                                </li>
                            </ol>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="buzzers.gameplay_title">Gameplay</h4>
                            <ol class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                    <span data-i18n="buzzers.gameplay1">Ask a question to all players</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                    <span data-i18n="buzzers.gameplay2">First player to buzz in gets to answer</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                    <span data-i18n="buzzers.gameplay3">Award points and continue to next question</span>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let gameActive = false;
            let gameStartTime = 0;
            let currentRound = 1;
            let totalRounds = 5;
            let scores = { 1: 0, 2: 0, 3: 0, 4: 0 };
            let buzzersLocked = false;
            let winner = null;

            // DOM elements
            const startGameBtn = document.getElementById('start-game');
            const resetGameBtn = document.getElementById('reset-game');
            const settingsBtn = document.getElementById('settings-btn');
            const winnerDisplay = document.getElementById('winner-display');
            const winnerText = document.getElementById('winner-text');
            const winnerTime = document.getElementById('winner-time');
            const gameStatus = document.getElementById('game-status');
            const roundInfo = document.getElementById('round-info');
            const currentRoundSpan = document.getElementById('current-round');
            const totalRoundsSpan = document.getElementById('total-rounds');

            // Buzzer elements
            const buzzers = {
                1: document.getElementById('buzzer-1'),
                2: document.getElementById('buzzer-2'),
                3: document.getElementById('buzzer-3'),
                4: document.getElementById('buzzer-4')
            };

            const scoreDisplays = {
                1: document.getElementById('score-1'),
                2: document.getElementById('score-2'),
                3: document.getElementById('score-3'),
                4: document.getElementById('score-4')
            };

            // Sound generation (Web Audio API)
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();

            function playBuzzerSound(frequency, duration = 0.3) {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                oscillator.type = 'square';

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            }

            // Buzzer sound frequencies
            const buzzerSounds = {
                1: 440,  // A4
                2: 523,  // C5
                3: 659,  // E5
                4: 784   // G5
            };

            // Show sound wave animation
            function showSoundWave(player) {
                const buzzer = buzzers[player];
                const soundIndicators = buzzer.querySelector('.sound-indicators');
                soundIndicators.classList.remove('hidden');

                setTimeout(() => {
                    soundIndicators.classList.add('hidden');
                }, 1000);
            }

            // Handle buzzer press
            function pressBuzzer(player) {
                if (!gameActive || buzzersLocked) return;

                const pressTime = Date.now();
                const reactionTime = pressTime - gameStartTime;

                // Lock all buzzers
                buzzersLocked = true;
                winner = player;

                // Visual feedback
                const buzzer = buzzers[player];
                buzzer.classList.add('pressed');

                // Sound feedback
                playBuzzerSound(buzzerSounds[player]);
                showSoundWave(player);

                // Show winner
                winnerText.textContent = `Player ${player} buzzed in first!`;
                winnerTime.textContent = `Reaction time: ${reactionTime}ms`;
                winnerDisplay.classList.remove('hidden');

                // Update game status
                gameStatus.textContent = `Player ${player} has the floor! Award point or continue.`;

                setTimeout(() => {
                    buzzer.classList.remove('pressed');
                }, 600);
            }

            // Start new game
            function startGame() {
                gameActive = true;
                gameStartTime = Date.now();
                buzzersLocked = false;
                winner = null;

                winnerDisplay.classList.add('hidden');
                gameStatus.textContent = 'Game active! First to buzz in wins!';
                roundInfo.classList.remove('hidden');

                startGameBtn.textContent = 'Next Round';
                startGameBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
                startGameBtn.classList.add('bg-blue-500', 'hover:bg-blue-600');
            }

            // Award point to winner
            function awardPoint() {
                if (winner) {
                    scores[winner]++;
                    updateScoreboard();

                    // Highlight winner in scoreboard
                    const playerScores = document.querySelectorAll('.player-score');
                    playerScores.forEach(score => score.classList.remove('winner'));
                    document.querySelector(`.player-score:nth-child(${winner})`).classList.add('winner');

                    setTimeout(() => {
                        document.querySelector(`.player-score:nth-child(${winner})`).classList.remove('winner');
                    }, 2000);
                }
            }

            // Update scoreboard
            function updateScoreboard() {
                Object.keys(scores).forEach(player => {
                    scoreDisplays[player].textContent = scores[player];
                });
            }

            // Reset game
            function resetGame() {
                gameActive = false;
                buzzersLocked = false;
                winner = null;
                currentRound = 1;
                scores = { 1: 0, 2: 0, 3: 0, 4: 0 };

                winnerDisplay.classList.add('hidden');
                roundInfo.classList.add('hidden');
                gameStatus.textContent = 'Ready to play! Press "Start Game" to begin.';

                startGameBtn.textContent = 'Start Game';
                startGameBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                startGameBtn.classList.add('bg-green-500', 'hover:bg-green-600');

                updateScoreboard();
                currentRoundSpan.textContent = currentRound;
            }

            // Event listeners
            startGameBtn.addEventListener('click', function() {
                if (gameActive && winner) {
                    awardPoint();

                    currentRound++;
                    currentRoundSpan.textContent = currentRound;

                    if (currentRound > totalRounds) {
                        // Game over
                        const maxScore = Math.max(...Object.values(scores));
                        const winners = Object.keys(scores).filter(player => scores[player] === maxScore);

                        if (winners.length === 1) {
                            gameStatus.textContent = `Game Over! Player ${winners[0]} wins with ${maxScore} points!`;
                        } else {
                            gameStatus.textContent = `Game Over! It's a tie between players ${winners.join(' and ')}!`;
                        }

                        gameActive = false;
                        roundInfo.classList.add('hidden');
                        startGameBtn.textContent = 'New Game';
                        return;
                    }
                }

                startGame();
            });

            resetGameBtn.addEventListener('click', resetGame);

            // Buzzer click events
            Object.keys(buzzers).forEach(player => {
                buzzers[player].addEventListener('click', () => pressBuzzer(player));
            });

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                const keyMap = { '1': 1, '2': 2, '3': 3, '4': 4 };
                const player = keyMap[e.key];

                if (player) {
                    e.preventDefault();
                    pressBuzzer(player);
                }

                // Space bar to start game
                if (e.code === 'Space' && !gameActive) {
                    e.preventDefault();
                    startGameBtn.click();
                }
            });

            // Settings (placeholder)
            settingsBtn.addEventListener('click', function() {
                const newRounds = prompt('How many rounds would you like to play?', totalRounds);
                if (newRounds && !isNaN(newRounds) && newRounds > 0) {
                    totalRounds = parseInt(newRounds);
                    totalRoundsSpan.textContent = totalRounds;
                }
            });

            // Initialize
            updateScoreboard();
            totalRoundsSpan.textContent = totalRounds;
        });
    </script>
</body>
</html>