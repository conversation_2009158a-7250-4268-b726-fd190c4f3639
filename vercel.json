{"version": 2, "name": "yesnooracle", "builds": [{"src": "**/*.html", "use": "@vercel/static"}, {"src": "src/**/*", "use": "@vercel/static"}], "routes": [{"src": "/", "dest": "/index.html"}, {"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/src/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).html", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}], "redirects": [{"source": "/oracle-page.html", "destination": "/yes-no-oracle.html", "permanent": true}, {"source": "/yes-and-no-oracle.html", "destination": "/yes-no-oracle.html", "permanent": true}]}