<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free Yes No Oracle - Get divine guidance for your questions with our ancient oracle system">
    <meta name="keywords" content="yes no oracle, divine guidance, spiritual answers, oracle questions, free oracle">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-no-oracle.html">
    <title>Yes No Oracle - Divine Guidance Tool | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .oracle-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 500px;
            position: relative;
        }
        .oracle-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>') repeat;
            opacity: 0.3;
        }
        .oracle-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Oracle section -->
    <section class="py-20 oracle-container relative">
        <div class="container mx-auto px-4 text-center relative z-10">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                <span data-i18n="oracle.title">Yes No Oracle</span>
            </h1>
            <p class="text-xl text-white/90 mb-12 max-w-2xl mx-auto" data-i18n="oracle.subtitle">
                Seek divine guidance for your most important questions. The ancient oracle awaits your inquiry.
            </p>

            <div class="max-w-2xl mx-auto oracle-card rounded-xl shadow-2xl p-8">
                <h2 class="text-2xl font-semibold mb-6" data-i18n="oracle.question">Ask the Oracle Your Question</h2>

                <textarea id="oracle-question" class="w-full p-4 border rounded-lg mb-6 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Type your question here..."
                    data-i18n-placeholder="oracle.placeholder"
                    rows="4"></textarea>

                <button id="consult-oracle" class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-lg text-xl font-bold hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105" data-i18n="oracle.consult">
                    Consult the Oracle
                </button>

                <div id="result-area" class="hidden mt-8">
                    <div class="border-t pt-6">
                        <p class="text-xl font-medium mb-4" data-i18n="oracle.answer">The Oracle says:</p>
                        <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6 mb-4">
                            <p id="result-text" class="text-3xl font-bold text-purple-800 result-animation"></p>
                        </div>
                        <p id="oracle-wisdom" class="text-gray-600 italic mb-4"></p>
                        <button id="ask-again" class="text-purple-600 underline hover:text-purple-800 transition-colors" data-i18n="oracle.ask_again">
                            Ask Another Question
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="oracle.features_title">About Our Yes No Oracle</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature1_title">Ancient Wisdom</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature1_desc">Based on centuries-old divination techniques for accurate guidance.</p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature2_title">Spiritual Insight</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature2_desc">Receive deeper spiritual answers beyond simple yes/no.</p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature3_title">Free Guidance</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature3_desc">Get divine answers without any cost or registration.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const consultBtn = document.getElementById('consult-oracle');
            const resultArea = document.getElementById('result-area');
            const resultText = document.getElementById('result-text');
            const oracleWisdom = document.getElementById('oracle-wisdom');
            const askAgainBtn = document.getElementById('ask-again');
            const questionInput = document.getElementById('oracle-question');

            const oracleAnswers = [
                { answer: 'Yes', wisdom: 'The universe aligns in your favor. Trust your instincts and move forward with confidence.' },
                { answer: 'No', wisdom: 'The cosmic forces suggest patience. This path may not serve your highest good at this time.' },
                { answer: 'Yes, clearly', wisdom: 'The signs are unmistakable. The oracle sees a bright path ahead for you.' },
                { answer: 'No, absolutely not', wisdom: 'The ancient wisdom warns against this course. Seek alternative paths.' },
                { answer: 'The signs point to yes', wisdom: 'While not entirely certain, the omens lean toward a positive outcome.' },
                { answer: 'The spirits say no', wisdom: 'The ethereal realm counsels caution. Consider waiting for a more auspicious time.' },
                { answer: 'Ask again later', wisdom: 'The cosmic energies are in flux. Return when the stars have shifted.' },
                { answer: 'The answer is unclear', wisdom: 'The veil between worlds obscures the truth. Meditate on your question and try again.' }
            ];

            consultBtn.addEventListener('click', function() {
                const question = questionInput.value.trim();
                if (!question) {
                    alert('Please enter a question for the oracle.');
                    return;
                }

                // Show loading state
                const originalText = showLoading(consultBtn);

                // Simulate oracle consultation delay
                setTimeout(() => {
                    const randomOracle = oracleAnswers[Math.floor(Math.random() * oracleAnswers.length)];
                    resultText.textContent = randomOracle.answer;
                    oracleWisdom.textContent = randomOracle.wisdom;
                    resultArea.classList.remove('hidden');

                    // Hide loading state
                    hideLoading(consultBtn, originalText);

                    // Scroll to result
                    resultArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 1500);
            });

            askAgainBtn.addEventListener('click', function() {
                resultArea.classList.add('hidden');
                questionInput.value = '';
                questionInput.focus();
            });

            // Allow Enter key to submit
            questionInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    consultBtn.click();
                }
            });
        });
    </script>
</body>
</html>