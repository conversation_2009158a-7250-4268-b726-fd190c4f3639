<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free Yes No Oracle - Get divine guidance for your questions with our ancient oracle system">
    <title>Yes No Oracle - Divine Guidance Tool | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Page-specific styles -->
    <style>
        .oracle-container {
            background: url('oracle-bg.jpg') center/cover no-repeat;
            min-height: 400px;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Oracle section -->
    <section class="py-20 oracle-container">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                <span data-i18n="oracle.title">Yes No Oracle</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white/90 rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-semibold mb-6" data-i18n="oracle.question">Ask the Oracle Your Question</h2>
                
                <textarea class="w-full p-4 border rounded-lg mb-6" placeholder="Type your question here..." data-i18n-placeholder="oracle.placeholder"></textarea>
                
                <button class="bg-purple-600 text-white px-8 py-4 rounded-lg text-xl font-bold hover:bg-purple-700 transition-colors" data-i18n="oracle.consult">Consult the Oracle</button>
                
                <div class="result-area hidden mt-8">
                    <p class="text-xl font-medium mb-4" data-i18n="oracle.answer">The Oracle says:</p>
                    <p class="text-3xl font-bold result-text"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="oracle.features_title">About Our Yes No Oracle</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature1_title">Ancient Wisdom</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature1_desc">Based on centuries-old divination techniques for accurate guidance.</p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature2_title">Spiritual Insight</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature2_desc">Receive deeper spiritual answers beyond simple yes/no.</p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="oracle.feature3_title">Free Guidance</h3>
                    <p class="text-gray-600" data-i18n="oracle.feature3_desc">Get divine answers without any cost or registration.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const consultBtn = document.querySelector('.consult-btn');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            consultBtn.addEventListener('click', function() {
                const answers = [
                    'Yes, clearly',
                    'No, absolutely not',
                    'The signs point to yes',
                    'The spirits say no',
                    'Ask again later',
                    'The answer is unclear'
                ];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                resultText.textContent = randomAnswer;
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>