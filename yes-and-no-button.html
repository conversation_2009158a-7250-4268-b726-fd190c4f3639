<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes and No Button - Simple binary decision maker for quick choices">
    <meta name="keywords" content="yes no button, binary choice, decision maker, quick decisions">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-and-no-button.html">
    <title>Yes and No Button - Binary Decision Maker | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .dual-container {
            background: linear-gradient(135deg, #10b981 0%, #059669 25%, #3b82f6 75%, #2563eb 100%);
            position: relative;
            overflow: hidden;
        }
        .dual-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dual-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="2" fill="white" opacity="0.1"/><circle cx="6" cy="6" r="1" fill="white" opacity="0.05"/><circle cx="19" cy="19" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23dual-pattern)"/></svg>') repeat;
        }
        .choice-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .choice-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            min-width: 150px;
            min-height: 150px;
        }
        .choice-button:hover {
            transform: scale(1.05);
        }
        .choice-button:active {
            transform: scale(0.95);
        }
        .choice-button.pressed {
            animation: choicePress 0.6s ease-out;
        }
        @keyframes choicePress {
            0% { transform: scale(1); }
            25% { transform: scale(0.9); }
            50% { transform: scale(1.1); }
            75% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .choice-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }
        .choice-button.pressed::before {
            width: 300px;
            height: 300px;
        }
        .yes-button {
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }
        .yes-button:hover {
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
        }
        .no-button {
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.3);
        }
        .no-button:hover {
            box-shadow: 0 15px 40px rgba(239, 68, 68, 0.4);
        }
        .result-display {
            animation: resultSlide 0.6s ease-out;
        }
        @keyframes resultSlide {
            0% { transform: translateY(50px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }
        .choice-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .battle-mode {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            animation: battleGlow 2s ease-in-out infinite alternate;
        }
        @keyframes battleGlow {
            from { box-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
            to { box-shadow: 0 0 30px rgba(251, 191, 36, 0.8); }
        }
        .vs-divider {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vs-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #d1d5db, transparent);
        }
        .vs-text {
            background: white;
            padding: 0 1rem;
            font-weight: bold;
            color: #6b7280;
            z-index: 1;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 dual-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="yesandnobutton.title">Yes & No Button</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="yesandnobutton.subtitle">
                    Simple binary choices for quick decisions. Choose your side in the eternal battle!
                </p>

                <!-- Choice Interface -->
                <div class="max-w-4xl mx-auto choice-card rounded-xl p-8 mb-8">
                    <!-- Question Input -->
                    <div class="mb-8">
                        <label for="question-input" class="block text-lg font-semibold mb-4" data-i18n="yesandnobutton.question_label">What's your question?</label>
                        <input type="text" id="question-input" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg" placeholder="Enter your question here..." data-i18n-placeholder="yesandnobutton.question_placeholder">
                    </div>

                    <!-- Choice Buttons -->
                    <div class="flex flex-col md:flex-row justify-center items-center gap-8 mb-8">
                        <button id="yes-button" class="choice-button yes-button bg-gradient-to-br from-green-500 to-green-700 text-white rounded-full text-3xl font-bold hover:from-green-600 hover:to-green-800 transition-all duration-300 flex flex-col items-center justify-center">
                            <span class="text-5xl mb-2">✓</span>
                            <span data-i18n="yesandnobutton.yes">YES</span>
                        </button>

                        <div class="vs-divider w-full md:w-20">
                            <span class="vs-text">VS</span>
                        </div>

                        <button id="no-button" class="choice-button no-button bg-gradient-to-br from-red-500 to-red-700 text-white rounded-full text-3xl font-bold hover:from-red-600 hover:to-red-800 transition-all duration-300 flex flex-col items-center justify-center">
                            <span class="text-5xl mb-2">✗</span>
                            <span data-i18n="yesandnobutton.no">NO</span>
                        </button>
                    </div>

                    <!-- Result Display -->
                    <div id="result-area" class="hidden mb-8">
                        <div class="result-display bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-6">
                            <div id="choice-result" class="text-4xl font-bold mb-4"></div>
                            <div id="choice-message" class="text-lg text-gray-700"></div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="choice-stats rounded-lg p-6 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="yesandnobutton.battle_stats">Battle Statistics</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="yes-count" class="text-3xl font-bold text-green-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="yesandnobutton.yes_wins">Yes Wins</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div id="total-decisions" class="text-3xl font-bold text-gray-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="yesandnobutton.total_decisions">Total Decisions</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div id="no-count" class="text-3xl font-bold text-red-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="yesandnobutton.no_wins">No Wins</div>
                            </div>
                        </div>

                        <!-- Win Percentage Bar -->
                        <div class="mt-6">
                            <div class="flex justify-between text-sm font-medium mb-2">
                                <span class="text-green-600" data-i18n="yesandnobutton.yes_percentage">Yes: <span id="yes-percentage">50%</span></span>
                                <span class="text-red-600" data-i18n="yesandnobutton.no_percentage">No: <span id="no-percentage">50%</span></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                                <div id="yes-bar" class="bg-green-500 h-4 transition-all duration-500" style="width: 50%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="reset-stats" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="yesandnobutton.reset">
                            Reset Stats
                        </button>
                        <button id="battle-mode" class="bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors" data-i18n="yesandnobutton.battle_mode">
                            Battle Mode
                        </button>
                    </div>
                </div>

                <!-- Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="yesandnobutton.feature1">Binary Choices</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="yesandnobutton.feature2">Statistics Tracking</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="yesandnobutton.feature3">Battle Mode</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="yesandnobutton.content_title">About Yes & No Button</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="yesandnobutton.content_subtitle">
                    The ultimate binary decision maker. Track your choices and see which side wins the battle!
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-green-100">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="yesandnobutton.feature1_title">Binary Decisions</h3>
                        <p class="text-gray-600" data-i18n="yesandnobutton.feature1_desc">Simple yes or no choices for quick decision making without overthinking.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-blue-100">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="yesandnobutton.feature2_title">Statistics Tracking</h3>
                        <p class="text-gray-600" data-i18n="yesandnobutton.feature2_desc">Track your decision patterns and see which choice you favor over time.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-yellow-100">
                        <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="yesandnobutton.feature3_title">Battle Mode</h3>
                        <p class="text-gray-600" data-i18n="yesandnobutton.feature3_desc">Engage battle mode for rapid-fire decisions and competitive choice making.</p>
                    </div>
                </div>

                <!-- Decision Making Tips -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="yesandnobutton.decision_tips">Decision Making Tips</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="yesandnobutton.quick_decisions">Quick Decisions</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="yesandnobutton.tip1">Trust your first instinct</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="yesandnobutton.tip2">Don't overthink simple choices</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="yesandnobutton.tip3">Use for low-stakes decisions</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-green-500 mr-2">✓</span>
                                    <span data-i18n="yesandnobutton.tip4">Practice decisive thinking</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="yesandnobutton.when_to_use">When to Use</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="yesandnobutton.use1">Choosing between two options</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="yesandnobutton.use2">Breaking decision paralysis</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="yesandnobutton.use3">Quick yes/no questions</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span data-i18n="yesandnobutton.use4">Fun group activities</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let yesCount = 0;
            let noCount = 0;
            let totalDecisions = 0;
            let battleMode = false;
            let battleTimer = null;

            // DOM elements
            const yesButton = document.getElementById('yes-button');
            const noButton = document.getElementById('no-button');
            const questionInput = document.getElementById('question-input');
            const resultArea = document.getElementById('result-area');
            const choiceResult = document.getElementById('choice-result');
            const choiceMessage = document.getElementById('choice-message');
            const resetStatsButton = document.getElementById('reset-stats');
            const battleModeButton = document.getElementById('battle-mode');

            // Statistics elements
            const yesCountDisplay = document.getElementById('yes-count');
            const noCountDisplay = document.getElementById('no-count');
            const totalDecisionsDisplay = document.getElementById('total-decisions');
            const yesPercentageDisplay = document.getElementById('yes-percentage');
            const noPercentageDisplay = document.getElementById('no-percentage');
            const yesBar = document.getElementById('yes-bar');

            // Choice messages
            const yesMessages = [
                "Great choice! Moving forward with confidence!",
                "Yes! Embracing opportunities ahead!",
                "Positive vibes! You're on the right track!",
                "Excellent! Taking action is key!",
                "Yes indeed! Trust your instincts!",
                "Wonderful! Saying yes opens doors!",
                "Perfect! Optimism leads to success!",
                "Fantastic! You're making progress!"
            ];

            const noMessages = [
                "Smart decision! Sometimes no is the right answer!",
                "Wise choice! Protecting your boundaries!",
                "Good call! Not every opportunity is worth it!",
                "Excellent! Knowing when to say no is powerful!",
                "Perfect! Selective choices lead to better outcomes!",
                "Great! Saying no can be liberating!",
                "Smart! Focus on what truly matters!",
                "Brilliant! Quality over quantity!"
            ];

            // Update statistics display
            function updateStatistics() {
                yesCountDisplay.textContent = yesCount;
                noCountDisplay.textContent = noCount;
                totalDecisionsDisplay.textContent = totalDecisions;

                if (totalDecisions > 0) {
                    const yesPercentage = Math.round((yesCount / totalDecisions) * 100);
                    const noPercentage = 100 - yesPercentage;

                    yesPercentageDisplay.textContent = yesPercentage + '%';
                    noPercentageDisplay.textContent = noPercentage + '%';
                    yesBar.style.width = yesPercentage + '%';
                } else {
                    yesPercentageDisplay.textContent = '50%';
                    noPercentageDisplay.textContent = '50%';
                    yesBar.style.width = '50%';
                }
            }

            // Show result with animation
            function showResult(choice, message) {
                choiceResult.textContent = choice;
                choiceResult.style.color = choice.includes('YES') ? '#10b981' : '#ef4444';
                choiceMessage.textContent = message;

                resultArea.classList.remove('hidden');

                // Auto-hide in battle mode
                if (battleMode) {
                    setTimeout(() => {
                        resultArea.classList.add('hidden');
                    }, 1500);
                } else {
                    setTimeout(() => {
                        resultArea.classList.add('hidden');
                    }, 4000);
                }
            }

            // Handle choice selection
            function makeChoice(choice) {
                const question = questionInput.value.trim();

                totalDecisions++;

                if (choice === 'yes') {
                    yesCount++;
                    const message = yesMessages[Math.floor(Math.random() * yesMessages.length)];
                    showResult('✓ YES!', question ? `"${question}" - ${message}` : message);

                    // Add press animation
                    yesButton.classList.add('pressed');
                    setTimeout(() => yesButton.classList.remove('pressed'), 600);
                } else {
                    noCount++;
                    const message = noMessages[Math.floor(Math.random() * noMessages.length)];
                    showResult('✗ NO!', question ? `"${question}" - ${message}` : message);

                    // Add press animation
                    noButton.classList.add('pressed');
                    setTimeout(() => noButton.classList.remove('pressed'), 600);
                }

                updateStatistics();

                // Clear question in battle mode
                if (battleMode) {
                    questionInput.value = '';
                }
            }

            // Reset statistics
            function resetStats() {
                yesCount = 0;
                noCount = 0;
                totalDecisions = 0;
                updateStatistics();
                resultArea.classList.add('hidden');
            }

            // Toggle battle mode
            function toggleBattleMode() {
                battleMode = !battleMode;

                if (battleMode) {
                    battleModeButton.textContent = 'Exit Battle';
                    battleModeButton.classList.remove('bg-yellow-500', 'hover:bg-yellow-600');
                    battleModeButton.classList.add('bg-red-500', 'hover:bg-red-600');

                    // Add battle mode styling
                    document.querySelector('.choice-card').classList.add('battle-mode');

                    // Start battle timer
                    let timeLeft = 60;
                    battleTimer = setInterval(() => {
                        timeLeft--;
                        battleModeButton.textContent = `Battle: ${timeLeft}s`;

                        if (timeLeft <= 0) {
                            toggleBattleMode();
                        }
                    }, 1000);

                    questionInput.placeholder = 'Quick! Enter your question and choose!';
                } else {
                    if (battleTimer) {
                        clearInterval(battleTimer);
                        battleTimer = null;
                    }

                    battleModeButton.textContent = 'Battle Mode';
                    battleModeButton.classList.remove('bg-red-500', 'hover:bg-red-600');
                    battleModeButton.classList.add('bg-yellow-500', 'hover:bg-yellow-600');

                    // Remove battle mode styling
                    document.querySelector('.choice-card').classList.remove('battle-mode');

                    questionInput.placeholder = 'Enter your question here...';

                    if (totalDecisions > 0) {
                        const winner = yesCount > noCount ? 'YES' : noCount > yesCount ? 'NO' : 'TIE';
                        alert(`Battle Over! Winner: ${winner}\nYes: ${yesCount} | No: ${noCount}`);
                    }
                }
            }

            // Event listeners
            yesButton.addEventListener('click', () => makeChoice('yes'));
            noButton.addEventListener('click', () => makeChoice('no'));
            resetStatsButton.addEventListener('click', resetStats);
            battleModeButton.addEventListener('click', toggleBattleMode);

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'y' || e.key === 'Y') {
                    e.preventDefault();
                    makeChoice('yes');
                }

                if (e.key === 'n' || e.key === 'N') {
                    e.preventDefault();
                    makeChoice('no');
                }

                if (e.key === 'r' || e.key === 'R') {
                    e.preventDefault();
                    resetStats();
                }

                if (e.key === 'b' || e.key === 'B') {
                    e.preventDefault();
                    toggleBattleMode();
                }

                if (e.key === 'Enter' && questionInput.value.trim()) {
                    questionInput.blur();
                }
            });

            // Initialize displays
            updateStatistics();
        });
    </script>
</body>
</html>