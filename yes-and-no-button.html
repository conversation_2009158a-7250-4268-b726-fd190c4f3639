<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes and No Button - Simple decision maker">
    <title>Yes and No Button | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Button section -->
    <section class="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="yesandnobutton.title">Yes and No Button</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="flex justify-center space-x-8 mb-8">
                    <button class="bg-green-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-green-700 transition-colors yes-button" data-i18n="yesandnobutton.yes">YES</button>
                    <button class="bg-red-600 text-white px-12 py-6 rounded-full text-2xl font-bold hover:bg-red-700 transition-colors no-button" data-i18n="yesandnobutton.no">NO</button>
                </div>
                
                <div class="result-area hidden mt-8">
                    <p class="text-5xl font-bold result-text" style="color: #2563eb;"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="yesandnobutton.content_title">About The Yes and No Button</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="yesandnobutton.content1">The Yes and No Button provides simple binary choices for quick decisions.</p>
                
                <p data-i18n="yesandnobutton.content2">This minimalist tool helps you make choices without overthinking - just press the button that feels right.</p>
                
                <p data-i18n="yesandnobutton.content3">Perfect for when you need a clear, immediate answer to a simple question.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yesBtn = document.querySelector('.yes-button');
            const noBtn = document.querySelector('.no-button');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            yesBtn.addEventListener('click', function() {
                resultText.textContent = 'YOU CHOSE YES';
                resultArea.classList.remove('hidden');
            });
            
            noBtn.addEventListener('click', function() {
                resultText.textContent = 'YOU CHOSE NO';
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>