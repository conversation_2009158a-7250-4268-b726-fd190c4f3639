# HTML文件关联指南

本文档说明如何将项目中的HTML文件更好地关联起来，创建统一的用户体验。

## 项目结构

```
yesnooracle/
├── index.html                 # 主页
├── button-maker.html         # 按钮制作器
├── yes-no-oracle.html        # 是否神谕
├── yes-no-button.html        # 是否按钮
├── ... (其他HTML文件)
├── src/
│   ├── components/
│   │   └── shared-header.html # 共享头部组件
│   ├── styles/
│   │   └── shared-styles.css  # 共享样式
│   ├── js/
│   │   └── shared-functions.js # 共享JavaScript功能
│   └── templates/
│       └── page-template.html # 页面模板
```

## 已实现的关联功能

### 1. 统一导航系统
- **主导航菜单**: 所有页面都有相同的导航结构
- **工具下拉菜单**: 包含所有工具页面的链接
- **面包屑导航**: 自动生成当前页面的导航路径
- **返回主页按钮**: 每个工具页面都有返回主页的链接

### 2. 共享样式系统
- **统一配色方案**: 
  - Primary: #0071e3 (蓝色)
  - Secondary: #1d1d1f (深灰)
  - Accent: #bf4800 (橙色)
- **一致的组件样式**: 按钮、卡片、表单等
- **响应式设计**: 适配移动端和桌面端
- **动画效果**: 统一的悬停和过渡效果

### 3. 多语言支持
- **语言选择器**: 支持英文、中文、日文、韩文、西班牙文
- **本地存储**: 记住用户的语言偏好
- **动态翻译**: 页面内容实时切换语言

### 4. 相关工具推荐
- **智能推荐**: 根据当前页面推荐相关工具
- **工具卡片**: 统一的工具展示样式
- **快速跳转**: 一键访问其他工具

## 如何使用共享组件

### 1. 创建新页面

使用 `src/templates/page-template.html` 作为模板：

```html
<!-- 替换以下占位符 -->
{{PAGE_TITLE}} - 页面标题
{{PAGE_DESCRIPTION}} - 页面描述
{{PAGE_KEYWORDS}} - SEO关键词
{{PAGE_URL}} - 页面URL
{{PAGE_SPECIFIC_STYLES}} - 页面特有样式
{{PAGE_CONTENT}} - 页面主要内容
{{PAGE_SPECIFIC_JAVASCRIPT}} - 页面特有JavaScript
```

### 2. 更新现有页面

按照以下步骤更新现有HTML文件：

1. **添加共享样式**:
```html
<link rel="stylesheet" href="src/styles/shared-styles.css">
```

2. **添加面包屑容器**:
```html
<div id="breadcrumb-container"></div>
```

3. **更新导航链接**:
确保所有导航链接指向正确的页面，使用 `index.html#section` 格式链接到主页的特定部分。

4. **添加相关工具部分**:
```html
<section class="related-tools">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
        <div id="related-tools-container"></div>
        <div class="text-center mt-12">
            <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
        </div>
    </div>
</section>
```

5. **添加共享JavaScript**:
```html
<script src="src/js/shared-functions.js"></script>
```

### 3. 添加多语言支持

为页面元素添加 `data-i18n` 属性：

```html
<h1 data-i18n="page.title">Page Title</h1>
<input placeholder="Enter text..." data-i18n-placeholder="page.placeholder">
```

在 `src/js/shared-functions.js` 中添加对应的翻译：

```javascript
const i18n = {
    en: {
        "page.title": "Page Title",
        "page.placeholder": "Enter text..."
    },
    zh: {
        "page.title": "页面标题",
        "page.placeholder": "输入文本..."
    }
};
```

## 样式类使用指南

### 按钮样式
```html
<button class="btn-primary">主要按钮</button>
<button class="btn-secondary">次要按钮</button>
```

### 工具卡片
```html
<div class="tool-card">
    <h3>工具名称</h3>
    <p>工具描述</p>
    <a href="tool.html" class="btn-primary">使用工具</a>
</div>
```

### 悬停效果
```html
<div class="card-hover">会有悬停动画的卡片</div>
```

## JavaScript功能

### 显示加载状态
```javascript
const originalText = showLoading(button);
// 执行异步操作
hideLoading(button, originalText);
```

### 获取当前页面信息
```javascript
const pageInfo = getCurrentPageInfo();
console.log(pageInfo.name, pageInfo.category);
```

## 最佳实践

1. **保持一致性**: 使用共享的样式类和组件
2. **SEO优化**: 为每个页面设置合适的meta标签
3. **可访问性**: 使用语义化HTML和适当的ARIA标签
4. **性能优化**: 压缩图片，使用CDN加载外部资源
5. **移动优先**: 确保所有页面在移动设备上正常显示

## 示例页面

参考 `yes-no-oracle.html` 查看完整的实现示例，该页面展示了：
- 统一的页面结构
- 共享组件的使用
- 多语言支持
- 相关工具推荐
- 响应式设计

## 下一步计划

1. **更新所有HTML文件**: 按照新的标准更新所有现有页面
2. **添加更多工具**: 使用模板快速创建新的工具页面
3. **优化SEO**: 改进页面的搜索引擎优化
4. **添加分析**: 集成Google Analytics或其他分析工具
5. **性能优化**: 实施缓存策略和资源优化

通过这些改进，您的HTML文件将形成一个统一、专业的网站体验，用户可以轻松在不同工具之间导航，同时保持一致的视觉和交互体验。
