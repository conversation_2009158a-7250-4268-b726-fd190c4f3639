<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or Mo - Get quick yes or no answers">
    <title>Yes or Mo | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Main interaction section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="mo.title">Yes or Mo</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="flex justify-center space-x-4 mb-8">
                    <button class="yes-btn bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors" data-i18n="mo.yes_button">YES</button>
                    <button class="mo-btn bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors" data-i18n="mo.mo_button">MO</button>
                </div>
                
                <div class="result-area hidden mt-8">
                    <p class="text-3xl font-bold result-text" style="color: #7c3aed;"></p>
                    <p class="text-lg mt-2 result-details"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="mo.content_title">About Yes or Mo</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="mo.content1">Our yes or mo tool provides quick answers to simple questions.</p>
                
                <p data-i18n="mo.content2">Perfect for when you need a straightforward yes or no response.</p>
                
                <p data-i18n="mo.content3">The "mo" option provides an alternative answer when yes/no doesn't fit.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yesBtn = document.querySelector('.yes-btn');
            const moBtn = document.querySelector('.mo-btn');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            const resultDetails = document.querySelector('.result-details');
            
            yesBtn.addEventListener('click', function() {
                resultText.textContent = 'YES';
                resultDetails.textContent = 'The answer is clearly yes!';
                resultArea.classList.remove('hidden');
            });
            
            moBtn.addEventListener('click', function() {
                resultText.textContent = 'MO';
                resultDetails.textContent = 'Neither yes nor no - the answer is mo!';
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>