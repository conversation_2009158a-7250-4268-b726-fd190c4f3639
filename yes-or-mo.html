<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or Mo - The fun alternative to yes/no decisions with quirky Mo answers">
    <meta name="keywords" content="yes or mo, fun decision maker, quirky answers, alternative to yes no">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-or-mo.html">
    <title>Yes or Mo - Fun Alternative Decision Maker | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .mo-container {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 25%, #dc2626 75%, #b91c1c 100%);
            position: relative;
            overflow: hidden;
        }
        .mo-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="mo-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="3" fill="white" opacity="0.1"/><circle cx="5" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="25" cy="5" r="1" fill="white" opacity="0.05"/><text x="10" y="20" font-size="8" fill="white" opacity="0.03">Mo</text></pattern></defs><rect width="100" height="100" fill="url(%23mo-pattern)"/></svg>') repeat;
        }
        .mo-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .choice-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            min-width: 200px;
            min-height: 120px;
        }
        .choice-button:hover {
            transform: scale(1.05);
        }
        .choice-button:active {
            transform: scale(0.95);
        }
        .choice-button.pressed {
            animation: moPress 0.6s ease-out;
        }
        @keyframes moPress {
            0% { transform: scale(1); }
            25% { transform: scale(0.9); }
            50% { transform: scale(1.1); }
            75% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .choice-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.5s ease;
        }
        .choice-button.pressed::before {
            width: 300px;
            height: 300px;
        }
        .yes-button {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }
        .yes-button:hover {
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
        }
        .mo-button {
            background: linear-gradient(135deg, #f97316, #ea580c);
            box-shadow: 0 10px 30px rgba(249, 115, 22, 0.3);
        }
        .mo-button:hover {
            box-shadow: 0 15px 40px rgba(249, 115, 22, 0.4);
        }
        .result-display {
            animation: moReveal 0.8s ease-out;
        }
        @keyframes moReveal {
            0% { transform: rotateX(90deg) scale(0.5); opacity: 0; }
            50% { transform: rotateX(45deg) scale(0.8); opacity: 0.7; }
            100% { transform: rotateX(0deg) scale(1); opacity: 1; }
        }
        .mo-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .fun-mode {
            background: linear-gradient(135deg, #f97316, #ea580c);
            animation: funGlow 2s ease-in-out infinite alternate;
        }
        @keyframes funGlow {
            from { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
            to { box-shadow: 0 0 30px rgba(249, 115, 22, 0.6); }
        }
        .mo-explanation {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            border-radius: 15px;
            position: relative;
        }
        .mo-explanation::before {
            content: '🤔';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 2rem;
            background: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .mo-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        .mo-example {
            background: white;
            border: 2px solid #f97316;
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.2s ease;
        }
        .mo-example:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.2);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 mo-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="mo.title">Yes or Mo</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="mo.subtitle">
                    When yes and no aren't enough, there's always "Mo" - the fun third option!
                </p>

                <!-- Yes or Mo Interface -->
                <div class="max-w-4xl mx-auto mo-card rounded-xl p-8 mb-8">
                    <!-- Question Input -->
                    <div class="mb-8">
                        <label for="question-input" class="block text-lg font-semibold mb-4" data-i18n="mo.question_label">What's your question?</label>
                        <input type="text" id="question-input" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-transparent text-center text-lg" placeholder="Ask something that needs a Yes or Mo answer..." data-i18n-placeholder="mo.question_placeholder">
                    </div>

                    <!-- Choice Buttons -->
                    <div class="flex flex-col md:flex-row justify-center items-center gap-8 mb-8">
                        <button id="yes-button" class="choice-button yes-button text-white rounded-xl text-2xl font-bold flex flex-col items-center justify-center">
                            <span class="text-4xl mb-2">✅</span>
                            <span data-i18n="mo.yes">YES</span>
                            <span class="text-sm opacity-75 mt-1" data-i18n="mo.yes_desc">The classic choice</span>
                        </button>

                        <div class="text-4xl text-gray-400 font-bold">OR</div>

                        <button id="mo-button" class="choice-button mo-button text-white rounded-xl text-2xl font-bold flex flex-col items-center justify-center">
                            <span class="text-4xl mb-2">🤷</span>
                            <span data-i18n="mo.mo">MO</span>
                            <span class="text-sm opacity-75 mt-1" data-i18n="mo.mo_desc">The fun alternative</span>
                        </button>
                    </div>

                    <!-- Result Display -->
                    <div id="result-area" class="hidden mb-8">
                        <div class="result-display bg-gradient-to-r from-orange-100 to-red-100 rounded-lg p-6">
                            <h3 class="text-2xl font-bold mb-4" data-i18n="mo.your_answer">Your Answer</h3>
                            <div id="question-display" class="text-lg text-gray-700 mb-4 italic"></div>
                            <div id="choice-result" class="text-5xl font-bold mb-4"></div>
                            <div id="choice-message" class="text-lg text-gray-700 mb-4"></div>
                            <div id="mo-explanation-text" class="text-sm text-gray-600 italic"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4 mb-6">
                        <button id="try-again-btn" class="hidden bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors" data-i18n="mo.try_again">
                            Try Again
                        </button>
                        <button id="fun-mode-btn" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors" data-i18n="mo.fun_mode">
                            Fun Mode
                        </button>
                        <button id="reset-stats-btn" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors" data-i18n="mo.reset_stats">
                            Reset Stats
                        </button>
                    </div>

                    <!-- Statistics -->
                    <div class="mo-stats rounded-lg p-6 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-center" data-i18n="mo.choice_stats">Choice Statistics</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div id="yes-count" class="text-3xl font-bold text-green-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="mo.yes_choices">Yes Choices</div>
                            </div>
                            <div class="text-center p-4 bg-orange-50 rounded-lg">
                                <div id="mo-count" class="text-3xl font-bold text-orange-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="mo.mo_choices">Mo Choices</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div id="total-choices" class="text-3xl font-bold text-blue-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="mo.total_choices">Total Choices</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div id="fun-choices" class="text-3xl font-bold text-purple-600">0</div>
                                <div class="text-sm text-gray-600" data-i18n="mo.fun_choices">Fun Mode Choices</div>
                            </div>
                        </div>
                    </div>

                    <!-- Mo Explanation -->
                    <div class="mo-explanation p-6 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-center pt-4" data-i18n="mo.what_is_mo">What is "Mo"?</h3>
                        <p class="text-gray-700 text-center mb-4" data-i18n="mo.mo_definition">
                            "Mo" is the perfect answer when yes and no just don't capture how you really feel. It's quirky, it's fun, and it's uniquely yours!
                        </p>
                        <div class="mo-examples">
                            <div class="mo-example">
                                <h4 class="font-semibold text-orange-700 mb-2" data-i18n="mo.example1_title">Mo = Maybe Later</h4>
                                <p class="text-sm text-gray-600" data-i18n="mo.example1_desc">When timing isn't right but the idea isn't wrong</p>
                            </div>
                            <div class="mo-example">
                                <h4 class="font-semibold text-orange-700 mb-2" data-i18n="mo.example2_title">Mo = More Info Needed</h4>
                                <p class="text-sm text-gray-600" data-i18n="mo.example2_desc">When you need to know more before deciding</p>
                            </div>
                            <div class="mo-example">
                                <h4 class="font-semibold text-orange-700 mb-2" data-i18n="mo.example3_title">Mo = Meh, Whatever</h4>
                                <p class="text-sm text-gray-600" data-i18n="mo.example3_desc">When you're feeling indifferent about the choice</p>
                            </div>
                            <div class="mo-example">
                                <h4 class="font-semibold text-orange-700 mb-2" data-i18n="mo.example4_title">Mo = Mood Dependent</h4>
                                <p class="text-sm text-gray-600" data-i18n="mo.example4_desc">When your answer depends on how you're feeling</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="flex justify-center space-x-8 text-white/80 text-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="mo.feature1">Fun Alternative</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="mo.feature2">Quirky Answers</span>
                    </div>
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span data-i18n="mo.feature3">Choice Tracking</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="mo.content_title">About Yes or Mo</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="mo.content_subtitle">
                    Break free from binary thinking with our fun alternative to traditional yes/no decisions.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-orange-100">
                        <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="mo.feature1_title">Fun Third Option</h3>
                        <p class="text-gray-600" data-i18n="mo.feature1_desc">When yes and no feel too limiting, "Mo" gives you the perfect quirky alternative.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-green-100">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="mo.feature2_title">Multiple Meanings</h3>
                        <p class="text-gray-600" data-i18n="mo.feature2_desc">"Mo" can mean maybe, more info needed, mood dependent, or whatever feels right to you.</p>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-purple-100">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2" data-i18n="mo.feature3_title">Choice Analytics</h3>
                        <p class="text-gray-600" data-i18n="mo.feature3_desc">Track your decision patterns and see how often you choose the unconventional "Mo" option.</p>
                    </div>
                </div>

                <!-- When to Use Mo -->
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-center mb-8" data-i18n="mo.when_to_use">When to Choose "Mo"</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="mo.perfect_for">Perfect Mo Situations</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-orange-500 mr-2">🤷</span>
                                    <span data-i18n="mo.situation1">When you're feeling indecisive</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-orange-500 mr-2">🤷</span>
                                    <span data-i18n="mo.situation2">When timing matters more than the choice</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-orange-500 mr-2">🤷</span>
                                    <span data-i18n="mo.situation3">When you need more information</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-orange-500 mr-2">🤷</span>
                                    <span data-i18n="mo.situation4">When your mood affects the answer</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold mb-4" data-i18n="mo.mo_meanings">What "Mo" Can Mean</h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="mo.meaning1">Maybe later, not now</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="mo.meaning2">More details needed first</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="mo.meaning3">Meh, I'm neutral about it</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-purple-500 mr-2">→</span>
                                    <span data-i18n="mo.meaning4">My own unique interpretation</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let yesCount = 0;
            let moCount = 0;
            let totalChoices = 0;
            let funChoices = 0;
            let funMode = false;

            // DOM elements
            const yesButton = document.getElementById('yes-button');
            const moButton = document.getElementById('mo-button');
            const questionInput = document.getElementById('question-input');
            const resultArea = document.getElementById('result-area');
            const questionDisplay = document.getElementById('question-display');
            const choiceResult = document.getElementById('choice-result');
            const choiceMessage = document.getElementById('choice-message');
            const moExplanationText = document.getElementById('mo-explanation-text');
            const tryAgainBtn = document.getElementById('try-again-btn');
            const funModeBtn = document.getElementById('fun-mode-btn');
            const resetStatsBtn = document.getElementById('reset-stats-btn');

            // Statistics elements
            const yesCountDisplay = document.getElementById('yes-count');
            const moCountDisplay = document.getElementById('mo-count');
            const totalChoicesDisplay = document.getElementById('total-choices');
            const funChoicesDisplay = document.getElementById('fun-choices');

            // Yes messages
            const yesMessages = [
                "Absolutely! Go for it with confidence!",
                "Yes indeed! The stars align in your favor.",
                "Definitely yes! Trust your instincts on this one.",
                "Yes! Sometimes the classic choice is the right choice.",
                "Affirmative! Full steam ahead!",
                "Yes, yes, yes! Embrace the opportunity!",
                "Certainly! Your enthusiasm is contagious.",
                "Yes! The universe approves of this decision."
            ];

            // Mo messages (regular)
            const moMessages = [
                "Mo! Sometimes the best answer isn't yes or no.",
                "Mo it is! Embrace the beautiful ambiguity.",
                "Mo! When conventional answers just don't fit.",
                "Mo! Your unique perspective shines through.",
                "Mo! Sometimes we need a third option.",
                "Mo! Breaking free from binary thinking.",
                "Mo! The perfect answer for an imperfect world.",
                "Mo! When your heart says 'it's complicated'."
            ];

            // Mo messages (fun mode)
            const funMoMessages = [
                "Mo-nificent choice! You're thinking outside the box!",
                "Mo-re power to you! Conventional answers are overrated!",
                "Mo-mentous decision! You've chosen the path less traveled!",
                "Mo-dern problems require Mo-dern solutions!",
                "Mo-st excellent! You're a true original!",
                "Mo-ving beyond yes and no - how revolutionary!",
                "Mo-re fun, less boring! You get it!",
                "Mo-st people would choose yes or no, but not you!"
            ];

            // Mo explanations
            const moExplanations = [
                "Mo can mean 'maybe later' - timing is everything!",
                "Mo might mean 'more info needed' - wisdom in waiting!",
                "Mo could be 'meh, whatever' - sometimes neutrality is perfect!",
                "Mo represents 'mood dependent' - feelings matter!",
                "Mo stands for 'my own interpretation' - make it yours!",
                "Mo means 'more options please' - why limit yourself?",
                "Mo suggests 'maybe in another context' - situation matters!",
                "Mo embodies 'magnificently open-minded' - stay flexible!"
            ];

            // Update statistics display
            function updateStatistics() {
                yesCountDisplay.textContent = yesCount;
                moCountDisplay.textContent = moCount;
                totalChoicesDisplay.textContent = totalChoices;
                funChoicesDisplay.textContent = funChoices;
            }

            // Make a choice
            function makeChoice(choice) {
                const question = questionInput.value.trim() || "Your question";

                totalChoices++;
                if (funMode) funChoices++;

                if (choice === 'yes') {
                    yesCount++;
                    const message = yesMessages[Math.floor(Math.random() * yesMessages.length)];
                    showResult('✅ YES!', message, question, '#10b981');

                    // Add press animation
                    yesButton.classList.add('pressed');
                    setTimeout(() => yesButton.classList.remove('pressed'), 600);
                } else {
                    moCount++;
                    const messages = funMode ? funMoMessages : moMessages;
                    const message = messages[Math.floor(Math.random() * messages.length)];
                    const explanation = moExplanations[Math.floor(Math.random() * moExplanations.length)];
                    showResult('🤷 MO!', message, question, '#f97316', explanation);

                    // Add press animation
                    moButton.classList.add('pressed');
                    setTimeout(() => moButton.classList.remove('pressed'), 600);
                }

                updateStatistics();
            }

            // Show result
            function showResult(result, message, question, color, explanation = '') {
                questionDisplay.textContent = `"${question}"`;
                choiceResult.textContent = result;
                choiceResult.style.color = color;
                choiceMessage.textContent = message;
                moExplanationText.textContent = explanation;

                resultArea.classList.remove('hidden');
                tryAgainBtn.classList.remove('hidden');

                // Auto-hide after 8 seconds
                setTimeout(() => {
                    if (!resultArea.classList.contains('hidden')) {
                        tryAgain();
                    }
                }, 8000);
            }

            // Try again
            function tryAgain() {
                resultArea.classList.add('hidden');
                tryAgainBtn.classList.add('hidden');
                questionInput.value = '';
                questionInput.focus();
            }

            // Toggle fun mode
            function toggleFunMode() {
                funMode = !funMode;

                if (funMode) {
                    funModeBtn.textContent = 'Exit Fun Mode';
                    funModeBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
                    funModeBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-600');

                    // Add fun mode styling
                    document.querySelector('.mo-card').classList.add('fun-mode');
                } else {
                    funModeBtn.textContent = 'Fun Mode';
                    funModeBtn.classList.remove('bg-yellow-500', 'hover:bg-yellow-600');
                    funModeBtn.classList.add('bg-red-600', 'hover:bg-red-700');

                    // Remove fun mode styling
                    document.querySelector('.mo-card').classList.remove('fun-mode');
                }
            }

            // Reset statistics
            function resetStats() {
                if (confirm('Are you sure you want to reset all statistics?')) {
                    yesCount = 0;
                    moCount = 0;
                    totalChoices = 0;
                    funChoices = 0;
                    updateStatistics();
                    tryAgain();
                }
            }

            // Event listeners
            yesButton.addEventListener('click', () => makeChoice('yes'));
            moButton.addEventListener('click', () => makeChoice('mo'));
            tryAgainBtn.addEventListener('click', tryAgain);
            funModeBtn.addEventListener('click', toggleFunMode);
            resetStatsBtn.addEventListener('click', resetStats);

            // Keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'y' || e.key === 'Y') {
                    e.preventDefault();
                    makeChoice('yes');
                }

                if (e.key === 'm' || e.key === 'M') {
                    e.preventDefault();
                    makeChoice('mo');
                }

                if (e.key === 'Enter' && questionInput.value.trim()) {
                    questionInput.blur();
                }

                if (e.key === 'r' || e.key === 'R') {
                    e.preventDefault();
                    tryAgain();
                }

                if (e.key === 'f' || e.key === 'F') {
                    e.preventDefault();
                    toggleFunMode();
                }
            });

            // Initialize
            updateStatistics();
            questionInput.focus();
        });
    </script>
</body>
</html>