<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Yes or Mo - The fun alternative to yes/no decisions with quirky Mo answers">
    <meta name="keywords" content="yes or mo, fun decision maker, quirky answers, alternative to yes no">
    <link rel="canonical" href="https://yesnooracle.xyz/yes-or-mo.html">
    <title>Yes or Mo - Fun Alternative Decision Maker | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .mo-container {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 25%, #dc2626 75%, #b91c1c 100%);
            position: relative;
            overflow: hidden;
        }
        .mo-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="mo-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="3" fill="white" opacity="0.1"/><circle cx="5" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="25" cy="5" r="1" fill="white" opacity="0.05"/><text x="10" y="20" font-size="8" fill="white" opacity="0.03">Mo</text></pattern></defs><rect width="100" height="100" fill="url(%23mo-pattern)"/></svg>') repeat;
        }
        .mo-card {
            backdrop-filter: blur(15px);
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .choice-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            transform: scale(1);
            min-width: 200px;
            min-height: 120px;
        }
        .choice-button:hover {
            transform: scale(1.05);
        }
        .choice-button:active {
            transform: scale(0.95);
        }
        .choice-button.pressed {
            animation: moPress 0.6s ease-out;
        }
        @keyframes moPress {
            0% { transform: scale(1); }
            25% { transform: scale(0.9); }
            50% { transform: scale(1.1); }
            75% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .choice-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.5s ease;
        }
        .choice-button.pressed::before {
            width: 300px;
            height: 300px;
        }
        .yes-button {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }
        .yes-button:hover {
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
        }
        .mo-button {
            background: linear-gradient(135deg, #f97316, #ea580c);
            box-shadow: 0 10px 30px rgba(249, 115, 22, 0.3);
        }
        .mo-button:hover {
            box-shadow: 0 15px 40px rgba(249, 115, 22, 0.4);
        }
        .result-display {
            animation: moReveal 0.8s ease-out;
        }
        @keyframes moReveal {
            0% { transform: rotateX(90deg) scale(0.5); opacity: 0; }
            50% { transform: rotateX(45deg) scale(0.8); opacity: 0.7; }
            100% { transform: rotateX(0deg) scale(1); opacity: 1; }
        }
        .mo-stats {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(148, 163, 184, 0.3);
        }
        .fun-mode {
            background: linear-gradient(135deg, #f97316, #ea580c);
            animation: funGlow 2s ease-in-out infinite alternate;
        }
        @keyframes funGlow {
            from { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
            to { box-shadow: 0 0 30px rgba(249, 115, 22, 0.6); }
        }
        .mo-explanation {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            border-radius: 15px;
            position: relative;
        }
        .mo-explanation::before {
            content: '🤔';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 2rem;
            background: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .mo-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        .mo-example {
            background: white;
            border: 2px solid #f97316;
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.2s ease;
        }
        .mo-example:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.2);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main interaction section -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="mo.title">Yes or Mo</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div class="flex justify-center space-x-4 mb-8">
                    <button class="yes-btn bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors" data-i18n="mo.yes_button">YES</button>
                    <button class="mo-btn bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors" data-i18n="mo.mo_button">MO</button>
                </div>
                
                <div class="result-area hidden mt-8">
                    <p class="text-3xl font-bold result-text" style="color: #7c3aed;"></p>
                    <p class="text-lg mt-2 result-details"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="mo.content_title">About Yes or Mo</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="mo.content1">Our yes or mo tool provides quick answers to simple questions.</p>
                
                <p data-i18n="mo.content2">Perfect for when you need a straightforward yes or no response.</p>
                
                <p data-i18n="mo.content3">The "mo" option provides an alternative answer when yes/no doesn't fit.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yesBtn = document.querySelector('.yes-btn');
            const moBtn = document.querySelector('.mo-btn');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            const resultDetails = document.querySelector('.result-details');
            
            yesBtn.addEventListener('click', function() {
                resultText.textContent = 'YES';
                resultDetails.textContent = 'The answer is clearly yes!';
                resultArea.classList.remove('hidden');
            });
            
            moBtn.addEventListener('click', function() {
                resultText.textContent = 'MO';
                resultDetails.textContent = 'Neither yes nor no - the answer is mo!';
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>