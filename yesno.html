<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free Yes No Decision Maker - Get quick answers to your yes/no questions">
    <title>Yes No Decision Maker | YesNoOracle</title>
    
    <!-- Reuse index.html CSS/JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white">
    <!-- Reuse header from index.html -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... existing header code ... -->
    </header>

    <!-- Decision Maker section -->
    <section class="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                <span data-i18n="yesno.title">Yes No Decision Maker</span>
            </h1>
            
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-semibold mb-6" data-i18n="yesno.question">Ask Your Yes/No Question</h2>
                
                <textarea class="w-full p-4 border rounded-lg mb-6" placeholder="Type your question here..." data-i18n-placeholder="yesno.placeholder"></textarea>
                
                <button class="bg-blue-600 text-white px-8 py-4 rounded-lg text-xl font-bold hover:bg-blue-700 transition-colors" data-i18n="yesno.decide">Get Answer</button>
                
                <div class="result-area hidden mt-8">
                    <p class="text-xl font-medium mb-4" data-i18n="yesno.answer">The answer is:</p>
                    <p class="text-3xl font-bold result-text"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="yesno.content_title">About Yes No Decisions</h2>
            
            <div class="prose max-w-3xl mx-auto">
                <p data-i18n="yesno.content1">Making decisions can be challenging, especially when you're stuck between two options. Our Yes No Decision Maker helps you get clear answers to your yes/no questions quickly and easily.</p>
                
                <p data-i18n="yesno.content2">Whether you're deciding about relationships, career choices, or everyday dilemmas, our tool provides instant guidance to help you move forward with confidence.</p>
                
                <p data-i18n="yesno.content3">The decision maker uses a sophisticated algorithm to analyze your question and provide the most appropriate yes or no answer based on your input.</p>
            </div>
        </div>
    </section>

    <!-- Reuse footer from index.html -->
    <footer class="bg-gray-100 py-12">
        <!-- ... existing footer code ... -->
    </footer>

    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const decideBtn = document.querySelector('.decide-btn');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            decideBtn.addEventListener('click', function() {
                const answers = [
                    'Yes',
                    'No',
                    'Definitely Yes',
                    'Definitely No',
                    'Probably Yes',
                    'Probably No'
                ];
                const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                resultText.textContent = randomAnswer;
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>