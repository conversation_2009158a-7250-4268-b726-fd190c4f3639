<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free Yes No Decision Maker - Get quick answers to your yes/no questions">
    <meta name="keywords" content="yesno, quick decision, simple choice, decision tool">
    <link rel="canonical" href="https://yesnooracle.xyz/yesno.html">
    <title>YesNo - Quick Decision Tool | YesNoOracle.xyz</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0071e3',
                        secondary: '#1d1d1f',
                        accent: '#bf4800'
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Shared Styles -->
    <link rel="stylesheet" href="src/styles/shared-styles.css">

    <!-- Page-specific styles -->
    <style>
        .decision-wheel {
            background: conic-gradient(from 0deg, #10b981 0deg 180deg, #ef4444 180deg 360deg);
            border-radius: 50%;
            position: relative;
            animation: spin 2s ease-out;
        }
        .decision-wheel.spinning {
            animation: spin 2s ease-out;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(720deg); }
        }
        .decision-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        .decision-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="white" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
        }
        .glass-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Breadcrumb Navigation -->
    <div id="breadcrumb-container"></div>

    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-primary">
                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                    <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                </svg>
                <a href="index.html" class="ml-2 text-xl font-semibold">YesNo<span class="text-primary">Oracle</span></a>
            </div>
            <nav class="hidden md:flex space-x-8">
                <div class="relative group">
                    <button class="text-gray-600 hover:text-primary transition-colors flex items-center" data-i18n="nav.tools">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div class="absolute hidden group-hover:block bg-white shadow-lg rounded-lg p-2 min-w-[200px]">
                        <a href="button-maker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_maker">Button Maker</a>
                        <a href="yes-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_button">Yes No Button</a>
                        <a href="yes-no-oracle.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle">Yes No Oracle</a>
                        <a href="yes-no-oracle-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_no_oracle_accurate">Yes No Oracle Accurate</a>
                        <a href="yesno.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yesno">YesNo</a>
                        <a href="no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_button">No Button</a>
                        <a href="button-clicker.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.button_clicker">Button Clicker</a>
                        <a href="game-buzzers.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.game_buzzers">Game Buzzers</a>
                        <a href="yes-and-no-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_and_no_button">Yes And No Button</a>
                        <a href="no-or-yes-button.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.no_or_yes_button">No Or Yes Button</a>
                        <a href="yes-or-no-tarot-accurate.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_accurate">Yes Or No Tarot Accurate</a>
                        <a href="yes-or-no-tarot-wheel.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_no_tarot_wheel">Yes Or No Tarot Wheel</a>
                        <a href="yes-or-mo.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded" data-i18n="tools.yes_or_mo">Yes Or Mo</a>
                    </div>
                </div>
                <a href="index.html" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.home">Home</a>
                <a href="index.html#how-it-works" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.how_it_works">How It Works</a>
                <a href="index.html#features" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.features">Features</a>
                <a href="index.html#testimonials" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.testimonials">Testimonials</a>
                <a href="index.html#pricing" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.pricing">Pricing</a>
                <a href="index.html#faq" class="text-gray-600 hover:text-primary transition-colors" data-i18n="nav.faq">FAQ</a>
            </nav>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="language-selector">
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="es">Español</option>
                </select>
                <a href="index.html" class="bg-primary text-white px-4 py-2 rounded-full hover:bg-blue-600 transition-colors" data-i18n="nav.try_free">Try Free</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="py-20 decision-container relative">
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                    <span data-i18n="yesno.title">YesNo Decision Maker</span>
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-12" data-i18n="yesno.subtitle">
                    The fastest way to make decisions. Ask your question and get an instant yes or no answer.
                </p>

                <!-- Decision Interface -->
                <div class="max-w-3xl mx-auto glass-card rounded-xl shadow-2xl p-8 mb-8">
                    <h2 class="text-2xl font-semibold mb-6" data-i18n="yesno.question">Ask Your Yes/No Question</h2>

                    <textarea id="question-input" class="w-full p-4 border rounded-lg mb-6 focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Type your question here..."
                        data-i18n-placeholder="yesno.placeholder"
                        rows="3"></textarea>

                    <!-- Decision Wheel -->
                    <div class="flex justify-center mb-8">
                        <div class="relative">
                            <div id="decision-wheel" class="decision-wheel w-32 h-32 flex items-center justify-center">
                                <div class="w-4 h-4 bg-white rounded-full absolute top-2 left-1/2 transform -translate-x-1/2"></div>
                            </div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg">
                                    <span class="text-sm font-bold text-gray-800">?</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button id="decide-btn" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-xl font-bold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105" data-i18n="yesno.decide">
                        Get Answer
                    </button>

                    <div id="result-area" class="hidden mt-8">
                        <div class="border-t pt-6">
                            <p class="text-xl font-medium mb-4" data-i18n="yesno.answer">The answer is:</p>
                            <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 mb-4">
                                <p id="result-text" class="text-4xl font-bold result-animation"></p>
                                <p id="confidence-text" class="text-sm text-gray-600 mt-2"></p>
                            </div>
                            <button id="try-again" class="text-primary underline hover:text-blue-600 transition-colors" data-i18n="yesno.try_again">
                                Ask Another Question
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-4" data-i18n="yesno.content_title">About YesNo Decisions</h2>
                <p class="text-gray-600 text-center max-w-2xl mx-auto mb-12" data-i18n="yesno.content_subtitle">
                    Sometimes the best decisions are the simplest ones. Let our decision wheel guide you.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
                    <div>
                        <h3 class="text-xl font-semibold mb-4" data-i18n="yesno.how_it_works">How It Works</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                    <span class="text-blue-600 font-bold text-sm">1</span>
                                </div>
                                <p class="text-gray-600" data-i18n="yesno.step1">Type your yes/no question in the text area</p>
                            </div>
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                    <span class="text-blue-600 font-bold text-sm">2</span>
                                </div>
                                <p class="text-gray-600" data-i18n="yesno.step2">Click the "Get Answer" button to spin the decision wheel</p>
                            </div>
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                                    <span class="text-blue-600 font-bold text-sm">3</span>
                                </div>
                                <p class="text-gray-600" data-i18n="yesno.step3">Get your instant yes or no answer with confidence level</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold mb-4" data-i18n="yesno.when_to_use">When to Use</h3>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span data-i18n="yesno.use1">Quick daily decisions</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span data-i18n="yesno.use2">Breaking decision paralysis</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span data-i18n="yesno.use3">Getting a fresh perspective</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span data-i18n="yesno.use4">Fun decision making</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Related Tools Section -->
    <section class="related-tools">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="common.related_tools">Related Tools</h2>
            <div id="related-tools-container">
                <!-- Related tools will be generated by JavaScript -->
            </div>
            <div class="text-center mt-12">
                <a href="index.html" class="btn-secondary" data-i18n="common.back_to_home">Back to Home</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 bg-secondary text-gray-300">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-8 h-8 text-white">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M9 9.5A1.5 1.5 0 1 1 7.5 8 1.5 1.5 0 0 1 9 9.5zM16.5 8A1.5 1.5 0 1 0 18 9.5 1.5 1.5 0 0 0 16.5 8z" fill="currentColor"/>
                            <path d="M8 14h8a4 4 0 0 1-8 0z" fill="currentColor"/>
                        </svg>
                        <span class="ml-2 text-xl font-semibold text-white">YesNo<span class="text-primary">Oracle</span></span>
                    </div>
                    <p class="mb-4" data-i18n="footer.description">Instant decision-making for the indecisive. Free, simple, reliable.</p>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.product">Product</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#how-it-works" class="hover:text-white transition-colors" data-i18n="footer.how_it_works">How It Works</a></li>
                        <li><a href="index.html#features" class="hover:text-white transition-colors" data-i18n="footer.features">Features</a></li>
                        <li><a href="index.html#pricing" class="hover:text-white transition-colors" data-i18n="footer.pricing">Pricing</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.company">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.about">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.blog">Blog</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-semibold mb-4" data-i18n="footer.support">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html#faq" class="hover:text-white transition-colors" data-i18n="footer.faq">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-i18n="footer.contact">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2023 YesNoOracle.xyz. <span data-i18n="footer.rights">All rights reserved.</span> <span data-i18n="footer.created_by">Created by</span> tangjei414</p>
                <p class="mt-2" data-i18n="footer.contact_label">Contact: <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white"><EMAIL></a></p>
            </div>
        </div>
    </footer>

    <!-- Shared JavaScript -->
    <script src="src/js/shared-functions.js"></script>

    <!-- Page-specific JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const decideBtn = document.getElementById('decide-btn');
            const resultArea = document.getElementById('result-area');
            const resultText = document.getElementById('result-text');
            const confidenceText = document.getElementById('confidence-text');
            const tryAgainBtn = document.getElementById('try-again');
            const questionInput = document.getElementById('question-input');
            const decisionWheel = document.getElementById('decision-wheel');

            const answers = [
                { answer: 'Yes', confidence: 'High confidence', color: '#10b981' },
                { answer: 'No', confidence: 'High confidence', color: '#ef4444' },
                { answer: 'Definitely Yes', confidence: 'Very high confidence', color: '#059669' },
                { answer: 'Definitely No', confidence: 'Very high confidence', color: '#dc2626' },
                { answer: 'Probably Yes', confidence: 'Medium confidence', color: '#34d399' },
                { answer: 'Probably No', confidence: 'Medium confidence', color: '#f87171' },
                { answer: 'Maybe', confidence: 'Low confidence', color: '#6b7280' },
                { answer: 'Ask Again Later', confidence: 'Unclear', color: '#9ca3af' }
            ];

            function spinWheel() {
                decisionWheel.classList.add('spinning');

                setTimeout(() => {
                    decisionWheel.classList.remove('spinning');
                }, 2000);
            }

            function showResult(answerObj) {
                resultText.textContent = answerObj.answer;
                resultText.style.color = answerObj.color;
                confidenceText.textContent = answerObj.confidence;

                resultArea.classList.remove('hidden');
                resultArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            decideBtn.addEventListener('click', function() {
                const question = questionInput.value.trim();
                if (!question) {
                    alert('Please enter a question first!');
                    questionInput.focus();
                    return;
                }

                const originalText = showLoading(decideBtn);

                // Spin the wheel
                spinWheel();

                setTimeout(() => {
                    hideLoading(decideBtn, originalText);
                    const randomAnswer = answers[Math.floor(Math.random() * answers.length)];
                    showResult(randomAnswer);
                }, 2200);
            });

            tryAgainBtn.addEventListener('click', function() {
                resultArea.classList.add('hidden');
                questionInput.value = '';
                questionInput.focus();
            });

            // Allow Enter key to submit
            questionInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    decideBtn.click();
                }
            });
        });
    </script>
</body>
</html>