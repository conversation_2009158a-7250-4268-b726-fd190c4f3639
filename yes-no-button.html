<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Free online Yes No Button tool - Make decisions with simple yes/no buttons">
    <title>Yes No Button - Simple Decision Maker | YesNoOracle</title>
    
    <!-- 复用index.html的CSS和JS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 页面特有样式 -->
    <style>
        .yes-btn {
            background-color: #4CAF50;
        }
        .no-btn {
            background-color: #F44336;
        }
    </style>
</head>
<body class="bg-white">
    <!-- 复用header -->
    <header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
        <!-- ... 复用index.html的header代码 ... -->
    </header>

    <!-- 页面特有内容 -->
    <section class="py-20 apple-gradient">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <span data-i18n="yesno_button.title">Yes No Button Decision Maker</span>
            </h1>
            
            <!-- 按钮交互区域 -->
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-semibold mb-6" data-i18n="yesno_button.question">Can't Decide? Let's Flip a Coin</h2>
                
                <div class="flex justify-center space-x-8 mb-8">
                    <button class="yes-btn text-white px-8 py-4 rounded-lg text-xl font-bold hover:bg-green-600 transition-colors" data-i18n="yesno_button.yes">YES</button>
                    <button class="no-btn text-white px-8 py-4 rounded-lg text-xl font-bold hover:bg-red-600 transition-colors" data-i18n="yesno_button.no">NO</button>
                </div>
                
                <div class="result-area hidden">
                    <p class="text-xl font-medium mb-4" data-i18n="yesno_button.result">The answer is:</p>
                    <p class="text-3xl font-bold result-text"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- 其他内容部分 -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12" data-i18n="yesno_button.features_title">Why Use Our Yes No Button?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 特性1 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="yesno_button.feature1_title">Instant Decision</h3>
                    <p class="text-gray-600" data-i18n="yesno_button.feature1_desc">Get a clear yes or no answer instantly for any dilemma.</p>
                </div>
                
                <!-- 特性2 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="yesno_button.feature2_title">Simple Interface</h3>
                    <p class="text-gray-600" data-i18n="yesno_button.feature2_desc">Just click one button and get your answer immediately.</p>
                </div>
                
                <!-- 特性3 -->
                <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                    <h3 class="text-xl font-semibold mb-2" data-i18n="yesno_button.feature3_title">No Registration</h3>
                    <p class="text-gray-600" data-i18n="yesno_button.feature3_desc">Completely free to use with no sign up required.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 复用footer -->
    <footer class="bg-gray-100 py-12">
        <!-- ... 复用index.html的footer代码 ... -->
    </footer>

    <!-- 页面特有JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yesBtn = document.querySelector('.yes-btn');
            const noBtn = document.querySelector('.no-btn');
            const resultArea = document.querySelector('.result-area');
            const resultText = document.querySelector('.result-text');
            
            yesBtn.addEventListener('click', function() {
                resultText.textContent = 'YES';
                resultArea.classList.remove('hidden');
            });
            
            noBtn.addEventListener('click', function() {
                resultText.textContent = 'NO';
                resultArea.classList.remove('hidden');
            });
        });
    </script>
</body>
</html>