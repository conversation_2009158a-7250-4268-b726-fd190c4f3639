/* Shared Styles for YesNoOracle Project */

/* Base styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    -webkit-font-smoothing: antialiased;
    color: #1d1d1f;
}

/* Apple-style gradient background */
.apple-gradient {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
}

/* Animation classes */
.result-animation {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

/* Hover effects */
.preset-question:hover {
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

/* Language selector */
.language-selector {
    background: transparent;
    border: none;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
}

.language-selector:focus {
    outline: none;
    background: rgba(0, 113, 227, 0.1);
}

/* Breadcrumb navigation */
.breadcrumb {
    background: rgba(0, 0, 0, 0.05);
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.breadcrumb-item {
    display: inline-flex;
    align-items: center;
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item:hover {
    color: #0071e3;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #9ca3af;
}

/* Tool card styles */
.tool-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Related tools section */
.related-tools {
    background: #f8fafc;
    padding: 48px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Button styles */
.btn-primary {
    background: #0071e3;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: #0071e3;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    border: 2px solid #0071e3;
    cursor: pointer;
}

.btn-secondary:hover {
    background: #0071e3;
    color: white;
    transform: translateY(-1px);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .tool-card:hover {
        transform: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0071e3;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-primary { color: #0071e3; }
.text-secondary { color: #1d1d1f; }
.text-accent { color: #bf4800; }
.bg-primary { background-color: #0071e3; }
.bg-secondary { background-color: #1d1d1f; }
.bg-accent { background-color: #bf4800; }

/* Focus states for accessibility */
.focus-visible:focus {
    outline: 2px solid #0071e3;
    outline-offset: 2px;
}
